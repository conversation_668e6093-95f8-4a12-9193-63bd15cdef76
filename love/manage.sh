#!/bin/bash

# 💕 Love Site 综合管理脚本
# 管理Love网站的所有操作，包括服务启动、停止、部署、监控等

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 项目路径
LOVE_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
WORKSPACE_DIR="$(dirname "$LOVE_DIR")"
DATA_DIR="$LOVE_DIR/data"
BACKUP_DIR="$DATA_DIR/backups"

# 加载环境变量配置
if [ -f "$LOVE_DIR/.env" ]; then
    echo -e "${CYAN}📝 加载环境配置文件: .env${NC}"
    export $(grep -v '^#' "$LOVE_DIR/.env" | xargs)
elif [ -f "$LOVE_DIR/.env.example" ]; then
    echo -e "${YELLOW}⚠️  未找到.env文件，使用.env.example作为默认配置${NC}"
    export $(grep -v '^#' "$LOVE_DIR/.env.example" | xargs)
fi

# 从环境变量或默认值获取配置
DOMAIN="${LOVE_DOMAIN_PRODUCTION:-love.yuh.cool}"
LOVE_PORT="${LOVE_PORT:-1314}"
LOVE_HOST="${LOVE_HOST:-0.0.0.0}"

# 宝塔面板配置
BT_NGINX_CONF="${BT_NGINX_CONF:-/www/server/panel/vhost/nginx/${DOMAIN}.conf}"
BT_SSL_DIR="${BT_SSL_DIR:-/www/server/panel/vhost/cert/${DOMAIN}}"

# 备用配置路径（如果不使用宝塔）
NGINX_CONF="${NGINX_CONF:-/etc/nginx/sites-available/${DOMAIN}.conf}"
NGINX_ENABLED="${NGINX_ENABLED:-/etc/nginx/sites-enabled/${DOMAIN}.conf}"

# 服务配置
LOVE_PID_FILE="$LOVE_DIR/backend.pid"
LOVE_LOG_FILE="$LOVE_DIR/logs/backend.log"
LOVE_DB_FILE="$DATA_DIR/love_messages.db"

# 显示标题
show_title() {
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    💕 Love Site Manager 💕                   ║"
    echo "║                     综合管理控制台                            ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# 显示状态
show_status() {
    echo -e "${CYAN}=== 💖 Love Site 服务状态 ===${NC}"
    
    # 检查后端服务
    if systemctl is-active --quiet love-site; then
        local pid=$(systemctl show --property MainPID --value love-site)
        echo -e "${GREEN}✅ Love Site服务: 运行中 (PID: $pid)${NC}"
        echo -e "   📍 端口: $LOVE_PORT (内部)"
        echo -e "   📝 日志: $LOVE_LOG_FILE"
        echo -e "   🔧 服务状态: $(systemctl is-active love-site)"
        echo -e "   🚀 开机自启: $(systemctl is-enabled love-site)"
    elif pgrep -f "node server.js" > /dev/null; then
        local pid=$(pgrep -f "node server.js")
        echo -e "${YELLOW}⚠️  Love Backend: 运行中 (PID: $pid) - 非系统服务${NC}"
        echo -e "   📍 端口: $LOVE_PORT"
        echo -e "   📝 日志: $LOVE_LOG_FILE"
    else
        echo -e "${RED}❌ Love Site服务: 未运行${NC}"
    fi
    
    # 检查宝塔面板反向代理配置
    echo -e "\n${CYAN}=== 🌐 宝塔面板状态 ===${NC}"
    if [ -f "$BT_NGINX_CONF" ]; then
        echo -e "${GREEN}✅ 宝塔站点配置: 存在${NC}"
        if grep -q "proxy_pass.*127.0.0.1:$LOVE_PORT" "$BT_NGINX_CONF" 2>/dev/null; then
            echo -e "${GREEN}✅ 反向代理配置: 正确${NC}"
        else
            echo -e "${YELLOW}⚠️  反向代理配置: 需要检查${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️  宝塔站点配置: 不存在或路径不正确${NC}"
        echo -e "   💡 请在宝塔面板中创建站点: $DOMAIN"
    fi
    
    # 检查端口
    echo -e "\n${CYAN}=== 🌐 端口状态 ===${NC}"
    if ss -tuln | grep ":$LOVE_PORT" > /dev/null; then
        echo -e "${GREEN}✅ 端口 $LOVE_PORT: 开放${NC}"
    else
        echo -e "${RED}❌ 端口 $LOVE_PORT: 关闭${NC}"
    fi
    
    if ss -tuln | grep ":443" > /dev/null; then
        echo -e "${GREEN}✅ HTTPS端口 443: 开放${NC}"
    else
        echo -e "${RED}❌ HTTPS端口 443: 关闭${NC}"
    fi
    
    # 检查数据库
    echo -e "\n${CYAN}=== 💾 数据库状态 ===${NC}"
    if [ -f "$LOVE_DB_FILE" ]; then
        local db_size=$(du -h "$LOVE_DB_FILE" | cut -f1)
        # 检查新表结构
        local msg_count_new=$(sqlite3 "$LOVE_DB_FILE" "SELECT COUNT(*) FROM love_messages_new WHERE status='active';" 2>/dev/null || echo "0")
        local msg_count_old=$(sqlite3 "$LOVE_DB_FILE" "SELECT COUNT(*) FROM love_messages;" 2>/dev/null || echo "0")
        local total_count=$((msg_count_new + msg_count_old))
        echo -e "${GREEN}✅ 数据库: 存在 (位置: data/love_messages.db)${NC}"
        echo -e "   📊 文件大小: $db_size"
        echo -e "   📊 留言总数: $total_count 条"
        if [ "$msg_count_new" -gt 0 ]; then
            echo -e "   📊 新表留言: $msg_count_new 条"
        fi
        if [ "$msg_count_old" -gt 0 ]; then
            echo -e "   📊 旧表留言: $msg_count_old 条"
        fi

        # 检查备份目录
        if [ -d "$BACKUP_DIR" ]; then
            local backup_count=$(ls -1 "$BACKUP_DIR"/*.db 2>/dev/null | wc -l)
            echo -e "   💾 备份文件: $backup_count 个"
        fi
    else
        echo -e "${RED}❌ 数据库: 不存在 (应在: data/love_messages.db)${NC}"
    fi
    
    # 检查SSL证书（宝塔面板管理）
    echo -e "\n${CYAN}=== 🔒 SSL证书状态 ===${NC}"
    if [ -f "$BT_SSL_DIR/fullchain.pem" ] || [ -f "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" ]; then
        if [ -f "$BT_SSL_DIR/fullchain.pem" ]; then
            local cert_expire=$(openssl x509 -in "$BT_SSL_DIR/fullchain.pem" -noout -enddate 2>/dev/null | cut -d= -f2 || echo "未知")
            echo -e "${GREEN}✅ SSL证书: 存在 (宝塔管理)${NC}"
        else
            local cert_expire=$(openssl x509 -in "/etc/letsencrypt/live/$DOMAIN/cert.pem" -noout -enddate 2>/dev/null | cut -d= -f2 || echo "未知")
            echo -e "${GREEN}✅ SSL证书: 存在 (Let's Encrypt)${NC}"
        fi
        echo -e "   📅 到期时间: $cert_expire"
    else
        echo -e "${RED}❌ SSL证书: 不存在${NC}"
        echo -e "   💡 请在宝塔面板中为域名 $DOMAIN 申请SSL证书"
    fi
    
    # 显示访问地址
    echo -e "\n${CYAN}=== 🌍 访问地址 ===${NC}"
    echo -e "${WHITE}🌐 网站首页: https://$DOMAIN/${NC}"
    echo -e "${WHITE}📅 在一起的日子: https://$DOMAIN/together-days${NC}"
    echo -e "${WHITE}🎉 纪念日: https://$DOMAIN/anniversary${NC}"
    echo -e "${WHITE}💑 相遇记录: https://$DOMAIN/meetings${NC}"
    echo -e "${WHITE}🎁 纪念页面: https://$DOMAIN/memorial${NC}"
    echo -e "${WHITE}🔗 API接口: https://$DOMAIN/api/${NC}"
    echo -e "${WHITE}🔧 API测试: https://$DOMAIN/test/test-api.html${NC}"
    echo -e "${CYAN}💡 提示: 现在使用独立域名 $DOMAIN，简洁URL路径，通过宝塔面板反向代理管理${NC}"

    # 仅在本地调试时显示localhost地址
    if systemctl is-active --quiet love-site || pgrep -f "node server.js" > /dev/null; then
        echo -e "\n${CYAN}=== 🛠️ 本地调试地址 (仅限服务器内部) ===${NC}"
        echo -e "${YELLOW}⚠️  以下地址仅用于服务器内部调试，请使用上方域名地址访问${NC}"
        echo -e "${WHITE}🔍 本地健康检查: http://localhost:$LOVE_PORT/api/health${NC}"
        echo -e "${WHITE}🐛 本地调试工具: http://localhost:$LOVE_PORT/debug-messages.html${NC}"
    fi
}

# 创建systemd服务
create_systemd_service() {
    echo -e "${CYAN}=== 🔧 创建Love Site系统服务 ===${NC}"

    # 确保日志目录存在
    mkdir -p "$(dirname "$LOVE_LOG_FILE")"

    # 获取node的实际路径
    local NODE_PATH=$(which node)
    if [[ -z "$NODE_PATH" ]]; then
        echo -e "${RED}❌ 错误: 找不到Node.js，请确保已安装Node.js${NC}"
        return 1
    fi

    echo -e "${BLUE}📍 Node.js 路径: $NODE_PATH${NC}"
    echo -e "${BLUE}📂 工作目录: $LOVE_DIR${NC}"
    echo -e "${BLUE}📝 日志文件: $LOVE_LOG_FILE${NC}"

    # 创建systemd服务文件
    sudo tee /etc/systemd/system/love-site.service > /dev/null <<EOF
[Unit]
Description=Love Site Backend Service
After=network.target
Wants=network.target

[Service]
Type=simple
User=root
WorkingDirectory=$LOVE_DIR
ExecStart=$NODE_PATH server.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production
Environment=PORT=1314

# 输出到日志
StandardOutput=append:$LOVE_LOG_FILE
StandardError=append:$LOVE_LOG_FILE

# 宽松的安全设置（适应更多环境）
NoNewPrivileges=false
ProtectSystem=false

[Install]
WantedBy=multi-user.target
EOF

    # 重新加载systemd配置
    sudo systemctl daemon-reload

    # 启用服务（开机自启）
    sudo systemctl enable love-site.service

    echo -e "${GREEN}✅ Love Site系统服务已创建并启用${NC}"
}

# 启动服务
start_services() {
    echo -e "${CYAN}=== 🚀 启动 Love Site 服务 ===${NC}"

    cd "$LOVE_DIR"

    # 检查依赖
    if [ ! -d "node_modules" ]; then
        echo -e "${YELLOW}📦 安装依赖...${NC}"
        npm install
    fi

    # 检查是否存在systemd服务
    if ! systemctl list-unit-files | grep -q "love-site.service"; then
        echo -e "${YELLOW}🔧 创建系统服务...${NC}"
        create_systemd_service
    fi

    # 启动后端服务（使用systemd）
    if systemctl is-active --quiet love-site; then
        echo -e "${YELLOW}⚠️  Love Site服务已在运行${NC}"
    else
        echo -e "${GREEN}🚀 启动Love Site服务...${NC}"
        sudo systemctl start love-site
        sleep 3

        if systemctl is-active --quiet love-site; then
            echo -e "${GREEN}✅ Love Site服务启动成功${NC}"
        else
            echo -e "${RED}❌ Love Site服务启动失败${NC}"
            echo -e "${YELLOW}查看日志: sudo journalctl -u love-site -f${NC}"
        fi
    fi

    # 启动Nginx
    if ! systemctl is-active --quiet nginx; then
        echo -e "${GREEN}🌐 启动Nginx...${NC}"
        sudo systemctl start nginx
    else
        echo -e "${YELLOW}⚠️  Nginx已在运行${NC}"
    fi

    echo -e "${GREEN}✅ 服务启动完成！${NC}"
    show_status
}

# 停止服务
stop_services() {
    echo -e "${CYAN}=== 🛑 停止 Love Site 服务 ===${NC}"

    # 停止systemd服务
    if systemctl is-active --quiet love-site; then
        echo -e "${YELLOW}🛑 停止Love Site服务...${NC}"
        sudo systemctl stop love-site
        echo -e "${GREEN}✅ Love Site服务已停止${NC}"
    else
        echo -e "${YELLOW}⚠️  Love Site服务未运行${NC}"
    fi

    # 备用：停止任何残留的node进程
    if pgrep -f "node server.js" > /dev/null; then
        echo -e "${YELLOW}🛑 停止残留的后端进程...${NC}"
        pkill -f "node server.js"
        echo -e "${GREEN}✅ 残留进程已清理${NC}"
    fi

    # 清理PID文件
    if [ -f "$LOVE_PID_FILE" ]; then
        rm -f "$LOVE_PID_FILE"
    fi

    echo -e "${GREEN}✅ 服务停止完成！${NC}"
}

# 重启服务
restart_services() {
    echo -e "${CYAN}=== 🔄 重启 Love Site 服务 ===${NC}"
    stop_services
    sleep 2
    start_services
}

# 查看日志
view_logs() {
    echo -e "${CYAN}=== 📝 Love Site 日志 ===${NC}"
    
    if [ -f "$LOVE_LOG_FILE" ]; then
        echo -e "${GREEN}📖 后端日志 (最近50行):${NC}"
        tail -50 "$LOVE_LOG_FILE"
        echo -e "\n${BLUE}💡 实时查看日志: tail -f $LOVE_LOG_FILE${NC}"
    else
        echo -e "${RED}❌ 日志文件不存在: $LOVE_LOG_FILE${NC}"
    fi
    
    echo -e "\n${GREEN}📖 Nginx错误日志 (最近10行):${NC}"
    sudo tail -10 /var/log/nginx/error.log 2>/dev/null || echo "无法读取Nginx日志"
}

# 现代情话管理
manage_modern_quotes() {
    echo -e "${CYAN}=== 💕 现代情话管理 ===${NC}"

    echo -e "${WHITE}选择操作:${NC}"
    echo -e "${GREEN}1)${NC} 查看现代情话统计"
    echo -e "${GREEN}2)${NC} 查看最新情话"
    echo -e "${GREEN}3)${NC} 按分类查看情话"
    echo -e "${GREEN}4)${NC} 导入更多情话数据"
    echo -e "${GREEN}5)${NC} 测试现代情话API"
    echo -e "${GREEN}6)${NC} 返回上级菜单"

    read -p "请选择 (1-6): " choice

    case $choice in
        1)
            echo -e "${CYAN}📊 现代情话统计:${NC}"
            if [ -f "$LOVE_DB_FILE" ]; then
                if sqlite3 "$LOVE_DB_FILE" ".tables" | grep -q "modern_love_quotes"; then
                    sqlite3 "$LOVE_DB_FILE" "
                        SELECT
                            COUNT(*) as total_quotes,
                            COUNT(DISTINCT category) as categories,
                            AVG(popularity_score) as avg_popularity,
                            MIN(beijing_datetime) as first_quote,
                            MAX(beijing_datetime) as last_quote
                        FROM modern_love_quotes WHERE status='active';
                    " -header -column
                    echo ""
                    echo "按分类统计:"
                    sqlite3 "$LOVE_DB_FILE" "
                        SELECT
                            category,
                            COUNT(*) as count,
                            AVG(popularity_score) as avg_popularity
                        FROM modern_love_quotes
                        WHERE status='active'
                        GROUP BY category
                        ORDER BY count DESC;
                    " -header -column
                else
                    echo -e "${RED}❌ 现代情话表不存在，请先启动服务器初始化数据${NC}"
                fi
            else
                echo -e "${RED}❌ 数据库文件不存在${NC}"
            fi
            ;;
        2)
            echo -e "${CYAN}📝 最新10条现代情话:${NC}"
            if [ -f "$LOVE_DB_FILE" ]; then
                if sqlite3 "$LOVE_DB_FILE" ".tables" | grep -q "modern_love_quotes"; then
                    sqlite3 "$LOVE_DB_FILE" "
                        SELECT
                            id,
                            category,
                            substr(content, 1, 60) || '...' as content_preview,
                            source,
                            popularity_score as popularity,
                            beijing_datetime as created_time
                        FROM modern_love_quotes
                        WHERE status='active'
                        ORDER BY created_timestamp DESC
                        LIMIT 10;
                    " -header -column
                else
                    echo -e "${RED}❌ 现代情话表不存在${NC}"
                fi
            else
                echo -e "${RED}❌ 数据库文件不存在${NC}"
            fi
            ;;
        3)
            echo -e "${CYAN}📂 按分类查看现代情话:${NC}"
            if [ -f "$LOVE_DB_FILE" ]; then
                if sqlite3 "$LOVE_DB_FILE" ".tables" | grep -q "modern_love_quotes"; then
                    echo "可用分类:"
                    sqlite3 "$LOVE_DB_FILE" "
                        SELECT category, COUNT(*) as count
                        FROM modern_love_quotes
                        WHERE status='active'
                        GROUP BY category;
                    " -header -column
                    echo ""
                    read -p "请输入要查看的分类 (romantic/sweet/deep/funny/proposal/anniversary/general): " category
                    if [ -n "$category" ]; then
                        sqlite3 "$LOVE_DB_FILE" "
                            SELECT
                                id,
                                substr(content, 1, 80) || '...' as content_preview,
                                source,
                                popularity_score
                            FROM modern_love_quotes
                            WHERE status='active' AND category='$category'
                            ORDER BY popularity_score DESC
                            LIMIT 20;
                        " -header -column
                    fi
                else
                    echo -e "${RED}❌ 现代情话表不存在${NC}"
                fi
            else
                echo -e "${RED}❌ 数据库文件不存在${NC}"
            fi
            ;;
        4)
            echo -e "${GREEN}📥 导入更多现代情话数据...${NC}"
            if [ -f "$LOVE_DIR/scripts/import-modern-quotes.js" ]; then
                cd "$LOVE_DIR"
                if command -v node >/dev/null 2>&1; then
                    echo "正在导入现代情话数据..."
                    node scripts/import-modern-quotes.js
                    echo -e "${GREEN}✅ 现代情话数据导入完成${NC}"
                else
                    echo -e "${RED}❌ Node.js 未安装，无法执行导入脚本${NC}"
                fi
            else
                echo -e "${RED}❌ 导入脚本不存在: $LOVE_DIR/scripts/import-modern-quotes.js${NC}"
            fi
            ;;
        5)
            echo -e "${CYAN}🧪 测试现代情话API:${NC}"
            echo "测试API接口..."
            echo ""
            echo "1. 获取统计信息:"
            curl -s "http://localhost:1314/api/modern-quotes/stats" | python3 -m json.tool 2>/dev/null || echo "API调用失败"
            echo ""
            echo "2. 获取随机情话:"
            curl -s "http://localhost:1314/api/modern-quotes/random" | python3 -m json.tool 2>/dev/null || echo "API调用失败"
            echo ""
            echo "3. 获取浪漫类情话 (前3条):"
            curl -s "http://localhost:1314/api/modern-quotes/category/romantic?limit=3" | python3 -m json.tool 2>/dev/null || echo "API调用失败"
            ;;
        6)
            return
            ;;
        *)
            echo -e "${RED}❌ 无效选择${NC}"
            ;;
    esac
}

# 数据库管理
manage_database() {
    echo -e "${CYAN}=== 💾 数据库管理 ===${NC}"

    # 确保data目录存在
    mkdir -p "$DATA_DIR" "$BACKUP_DIR"

    echo -e "${WHITE}选择操作:${NC}"
    echo -e "${GREEN}1)${NC} 查看数据库信息"
    echo -e "${GREEN}2)${NC} 查看留言统计"
    echo -e "${GREEN}3)${NC} 查看最近留言"
    echo -e "${GREEN}4)${NC} 现代情话管理"
    echo -e "${GREEN}5)${NC} 备份数据库"
    echo -e "${GREEN}6)${NC} 恢复数据库"
    echo -e "${GREEN}7)${NC} 管理备份文件"
    echo -e "${GREEN}8)${NC} 数据库维护"
    echo -e "${GREEN}9)${NC} 清空所有留言"
    echo -e "${GREEN}10)${NC} 返回主菜单"

    read -p "请选择 (1-10): " choice

    case $choice in
        1)
            echo -e "${CYAN}📊 数据库信息:${NC}"
            if [ -f "$LOVE_DB_FILE" ]; then
                echo "数据库文件: $LOVE_DB_FILE"
                echo "文件大小: $(du -h "$LOVE_DB_FILE" | cut -f1)"
                echo "创建时间: $(stat -c %y "$LOVE_DB_FILE")"
                echo "最后修改: $(stat -c %y "$LOVE_DB_FILE")"
                echo ""
                echo "表结构:"
                sqlite3 "$LOVE_DB_FILE" ".tables"
                echo ""
                echo "数据库完整性检查:"
                sqlite3 "$LOVE_DB_FILE" "PRAGMA integrity_check;"
            else
                echo -e "${RED}❌ 数据库文件不存在${NC}"
            fi
            ;;
        2)
            echo -e "${CYAN}📊 留言统计:${NC}"
            if [ -f "$LOVE_DB_FILE" ]; then
                # 检查新表
                if sqlite3 "$LOVE_DB_FILE" ".tables" | grep -q "love_messages_new"; then
                    sqlite3 "$LOVE_DB_FILE" "
                        SELECT
                            COUNT(*) as total_messages,
                            COUNT(DISTINCT author) as unique_authors,
                            MIN(beijing_datetime) as first_message,
                            MAX(beijing_datetime) as last_message
                        FROM love_messages_new WHERE status='active';
                    " -header -column
                else
                    sqlite3 "$LOVE_DB_FILE" "
                        SELECT
                            COUNT(*) as total_messages,
                            COUNT(DISTINCT author) as unique_authors,
                            MIN(datetime(created_at, 'unixepoch')) as first_message,
                            MAX(datetime(created_at, 'unixepoch')) as last_message
                        FROM love_messages;
                    " -header -column
                fi
            else
                echo -e "${RED}❌ 数据库文件不存在${NC}"
            fi
            ;;
        3)
            echo -e "${CYAN}📝 最近10条留言:${NC}"
            if [ -f "$LOVE_DB_FILE" ]; then
                # 检查新表
                if sqlite3 "$LOVE_DB_FILE" ".tables" | grep -q "love_messages_new"; then
                    sqlite3 "$LOVE_DB_FILE" "
                        SELECT
                            id,
                            author,
                            substr(content, 1, 50) || '...' as content_preview,
                            beijing_datetime as created_time
                        FROM love_messages_new
                        WHERE status='active'
                        ORDER BY created_timestamp DESC
                        LIMIT 10;
                    " -header -column
                else
                    sqlite3 "$LOVE_DB_FILE" "
                        SELECT
                            id,
                            author,
                            substr(content, 1, 50) || '...' as content_preview,
                            datetime(created_at, 'unixepoch') as created_time
                        FROM love_messages
                        ORDER BY created_at DESC
                        LIMIT 10;
                    " -header -column
                fi
            else
                echo -e "${RED}❌ 数据库文件不存在${NC}"
            fi
            ;;
        4)
            manage_modern_quotes
            ;;
        5)
            echo -e "${GREEN}💾 备份数据库...${NC}"
            if [ -f "$LOVE_DB_FILE" ]; then
                local backup_file="$BACKUP_DIR/love_messages_backup_$(date +%Y%m%d_%H%M%S).db"
                cp "$LOVE_DB_FILE" "$backup_file"
                echo -e "${GREEN}✅ 数据库已备份到: $backup_file${NC}"
                echo -e "   备份大小: $(du -h "$backup_file" | cut -f1)"
            else
                echo -e "${RED}❌ 数据库文件不存在，无法备份${NC}"
            fi
            ;;
        5)
            echo -e "${YELLOW}📂 恢复数据库...${NC}"
            if [ -d "$BACKUP_DIR" ] && [ "$(ls -A "$BACKUP_DIR"/*.db 2>/dev/null)" ]; then
                echo "可用备份文件:"
                ls -la "$BACKUP_DIR"/*.db | awk '{print NR". "$9" ("$5" bytes, "$6" "$7" "$8")"}'
                echo ""
                read -p "请输入备份文件的完整路径或文件名: " backup_input

                # 如果只输入文件名，添加完整路径
                if [[ "$backup_input" != /* ]]; then
                    backup_file="$BACKUP_DIR/$backup_input"
                else
                    backup_file="$backup_input"
                fi

                if [ -f "$backup_file" ]; then
                    # 先备份当前数据库
                    if [ -f "$LOVE_DB_FILE" ]; then
                        local current_backup="$BACKUP_DIR/current_backup_before_restore_$(date +%Y%m%d_%H%M%S).db"
                        cp "$LOVE_DB_FILE" "$current_backup"
                        echo -e "${YELLOW}当前数据库已备份到: $current_backup${NC}"
                    fi

                    # 恢复数据库
                    cp "$backup_file" "$LOVE_DB_FILE"
                    echo -e "${GREEN}✅ 数据库已从备份恢复${NC}"
                    echo -e "   恢复文件: $backup_file"

                    # 重启服务以应用更改
                    if systemctl is-active --quiet love-site; then
                        echo -e "${YELLOW}🔄 重启服务以应用数据库更改...${NC}"
                        sudo systemctl restart love-site
                    fi
                else
                    echo -e "${RED}❌ 备份文件不存在: $backup_file${NC}"
                fi
            else
                echo -e "${RED}❌ 没有找到备份文件${NC}"
            fi
            ;;
        6)
            echo -e "${CYAN}📂 备份文件管理:${NC}"
            if [ -d "$BACKUP_DIR" ] && [ "$(ls -A "$BACKUP_DIR"/*.db 2>/dev/null)" ]; then
                echo "备份文件列表:"
                ls -lah "$BACKUP_DIR"/*.db | awk '{print NR". "$9" ("$5", "$6" "$7" "$8")"}'
                echo ""
                echo "操作选项:"
                echo "1) 删除旧备份文件"
                echo "2) 查看备份文件详情"
                echo "3) 返回"
                read -p "请选择: " backup_choice

                case $backup_choice in
                    1)
                        echo "删除多少天前的备份文件？"
                        read -p "输入天数 (例如: 30): " days
                        if [[ "$days" =~ ^[0-9]+$ ]]; then
                            find "$BACKUP_DIR" -name "*.db" -type f -mtime +$days -delete
                            echo -e "${GREEN}✅ 已删除 $days 天前的备份文件${NC}"
                        else
                            echo -e "${RED}❌ 无效的天数${NC}"
                        fi
                        ;;
                    2)
                        echo "备份文件详情:"
                        for file in "$BACKUP_DIR"/*.db; do
                            if [ -f "$file" ]; then
                                echo "文件: $(basename "$file")"
                                echo "大小: $(du -h "$file" | cut -f1)"
                                echo "时间: $(stat -c %y "$file")"
                                echo "---"
                            fi
                        done
                        ;;
                esac
            else
                echo -e "${YELLOW}⚠️  没有找到备份文件${NC}"
            fi
            ;;
        8)
            echo -e "${CYAN}🔧 数据库维护:${NC}"
            if [ -f "$LOVE_DB_FILE" ]; then
                echo "1) 数据库完整性检查"
                echo "2) 优化数据库"
                echo "3) 重建索引"
                echo "4) 数据库统计信息"
                read -p "请选择: " maint_choice

                case $maint_choice in
                    1)
                        echo "检查数据库完整性..."
                        sqlite3 "$LOVE_DB_FILE" "PRAGMA integrity_check;"
                        ;;
                    2)
                        echo "优化数据库..."
                        sqlite3 "$LOVE_DB_FILE" "VACUUM;"
                        echo -e "${GREEN}✅ 数据库优化完成${NC}"
                        ;;
                    3)
                        echo "重建索引..."
                        sqlite3 "$LOVE_DB_FILE" "REINDEX;"
                        echo -e "${GREEN}✅ 索引重建完成${NC}"
                        ;;
                    4)
                        echo "数据库统计信息:"
                        sqlite3 "$LOVE_DB_FILE" "
                            SELECT
                                name as table_name,
                                sql as create_statement
                            FROM sqlite_master
                            WHERE type='table';
                        " -header -column
                        ;;
                esac
            else
                echo -e "${RED}❌ 数据库文件不存在${NC}"
            fi
            ;;
        9)
            echo -e "${RED}⚠️  危险操作：清空所有留言${NC}"
            echo -e "${YELLOW}这将删除所有留言数据，此操作不可恢复！${NC}"
            read -p "确定要清空所有留言吗？(输入 'DELETE_ALL_MESSAGES' 确认): " confirm
            if [ "$confirm" = "DELETE_ALL_MESSAGES" ]; then
                # 先备份
                local backup_file="$BACKUP_DIR/backup_before_clear_$(date +%Y%m%d_%H%M%S).db"
                cp "$LOVE_DB_FILE" "$backup_file"
                echo -e "${YELLOW}数据已备份到: $backup_file${NC}"

                # 清空数据
                if sqlite3 "$LOVE_DB_FILE" ".tables" | grep -q "love_messages_new"; then
                    sqlite3 "$LOVE_DB_FILE" "DELETE FROM love_messages_new;"
                fi
                sqlite3 "$LOVE_DB_FILE" "DELETE FROM love_messages;" 2>/dev/null || true

                echo -e "${GREEN}✅ 所有留言已清空${NC}"

                # 重启服务
                if systemctl is-active --quiet love-site; then
                    echo -e "${YELLOW}🔄 重启服务...${NC}"
                    sudo systemctl restart love-site
                fi
            else
                echo -e "${YELLOW}操作已取消${NC}"
            fi
            ;;
        10)
            return
            ;;
        *)
            echo -e "${RED}❌ 无效选择${NC}"
            ;;
    esac
}

# 部署管理
deploy_management() {
    echo -e "${CYAN}=== 🚀 部署管理 ===${NC}"

    echo -e "${WHITE}选择操作:${NC}"
    echo -e "${GREEN}1)${NC} 部署到生产环境"
    echo -e "${GREEN}2)${NC} 配置完整Nginx（包含New-API+Love）"
    echo -e "${GREEN}3)${NC} 验证所有服务状态"
    echo -e "${GREEN}4)${NC} 测试外部访问"
    echo -e "${GREEN}5)${NC} 更新SSL证书"
    echo -e "${GREEN}6)${NC} 检查配置"
    echo -e "${GREEN}7)${NC} 返回主菜单"

    read -p "请选择 (1-7): " choice

    case $choice in
        1)
            echo -e "${GREEN}🚀 部署到生产环境...${NC}"

            # 创建完整备份
            echo -e "${YELLOW}📦 创建部署前备份...${NC}"
            local backup_name="full_backup_$(date +%Y%m%d_%H%M%S)"
            local backup_path="$BACKUP_DIR/$backup_name"
            mkdir -p "$backup_path"

            # 备份数据库
            if [ -f "$LOVE_DB_FILE" ]; then
                cp "$LOVE_DB_FILE" "$backup_path/"
                echo "✅ 数据库已备份"
            fi

            # 备份配置文件
            cp -r "$LOVE_DIR/config" "$backup_path/"
            echo "✅ 配置文件已备份"

            # 备份日志
            if [ -d "$LOVE_DIR/logs" ]; then
                cp -r "$LOVE_DIR/logs" "$backup_path/"
                echo "✅ 日志文件已备份"
            fi

            # 部署服务
            echo -e "${GREEN}🚀 部署Love Site服务...${NC}"

            # 停止服务
            if systemctl is-active --quiet love-site; then
                sudo systemctl stop love-site
            fi

            # 检查宝塔面板反向代理配置
            echo -e "${YELLOW}📝 检查宝塔面板反向代理配置...${NC}"
            echo -e "${CYAN}ℹ️  现在使用宝塔面板管理反向代理，无需手动配置Nginx${NC}"
            if [ -f "$BT_NGINX_CONF" ]; then
                echo -e "${GREEN}✅ 宝塔面板配置文件存在: $BT_NGINX_CONF${NC}"
                if grep -q "proxy_pass.*127.0.0.1:$LOVE_PORT" "$BT_NGINX_CONF" 2>/dev/null; then
                    echo -e "${GREEN}✅ 反向代理配置正确${NC}"
                else
                    echo -e "${YELLOW}⚠️  请在宝塔面板中配置反向代理到 http://127.0.0.1:$LOVE_PORT${NC}"
                fi
            else
                echo -e "${YELLOW}⚠️  宝塔面板配置文件不存在，请先在宝塔面板中创建站点 $DOMAIN${NC}"
            fi

            # 重新创建并启动服务
            create_systemd_service
            sudo systemctl start love-site

            if systemctl is-active --quiet love-site; then
                echo -e "${GREEN}✅ 部署完成！${NC}"
                echo -e "备份位置: $backup_path"
            else
                echo -e "${RED}❌ 服务启动失败${NC}"
            fi
            ;;
        2)
            echo -e "${GREEN}🌐 检查宝塔面板配置...${NC}"
            echo -e "${CYAN}ℹ️  现在使用宝塔面板管理反向代理，无需手动配置Nginx${NC}"

            echo -e "\n${CYAN}=== 宝塔面板配置检查 ===${NC}"
            if [ -f "$BT_NGINX_CONF" ]; then
                echo -e "${GREEN}✅ 宝塔站点配置存在: $DOMAIN${NC}"

                # 检查反向代理配置
                if grep -q "proxy_pass.*127.0.0.1:$LOVE_PORT" "$BT_NGINX_CONF" 2>/dev/null; then
                    echo -e "${GREEN}✅ 反向代理配置正确 (端口: $LOVE_PORT)${NC}"
                else
                    echo -e "${YELLOW}⚠️  反向代理配置可能不正确${NC}"
                    echo -e "${CYAN}请在宝塔面板中配置:${NC}"
                    echo -e "   - 站点设置 → 反向代理"
                    echo -e "   - 目标URL: http://127.0.0.1:$LOVE_PORT"
                    echo -e "   - 代理目录: /"
                fi

                # 检查SSL配置
                if grep -q "ssl_certificate" "$BT_NGINX_CONF" 2>/dev/null; then
                    echo -e "${GREEN}✅ SSL配置存在${NC}"
                else
                    echo -e "${YELLOW}⚠️  SSL配置不存在，请在宝塔面板中申请SSL证书${NC}"
                fi
            else
                echo -e "${RED}❌ 宝塔站点配置不存在${NC}"
                echo -e "${CYAN}请先在宝塔面板中:${NC}"
                echo -e "   1. 创建站点: $DOMAIN"
                echo -e "   2. 配置反向代理到: http://127.0.0.1:$LOVE_PORT"
                echo -e "   3. 申请SSL证书"
            fi
            ;;
        3)
            echo -e "${GREEN}🔍 验证所有服务状态...${NC}"

            echo -e "${CYAN}=== New-API 服务状态 ===${NC}"
            cd "$WORKSPACE_DIR/new-api"
            if [ -f "./new-api-manager.sh" ]; then
                ./new-api-manager.sh status
            else
                echo -e "${RED}❌ New-API管理脚本不存在${NC}"
            fi

            echo -e "${CYAN}=== Love 服务状态 ===${NC}"
            cd "$LOVE_DIR"
            if systemctl is-active --quiet love-site; then
                echo -e "${GREEN}✅ Love服务运行正常${NC}"
                echo -e "  状态: $(systemctl is-active love-site)"
                echo -e "  端口: $LOVE_PORT"
                if ss -tuln | grep -q ":$LOVE_PORT"; then
                    echo -e "${GREEN}✅ 端口$LOVE_PORT正在监听${NC}"
                else
                    echo -e "${YELLOW}⚠️  端口$LOVE_PORT未在监听${NC}"
                fi
            else
                echo -e "${YELLOW}⚠️  Love服务未运行${NC}"
            fi

            echo -e "${CYAN}=== Nginx 服务状态 ===${NC}"
            if systemctl is-active --quiet nginx; then
                echo -e "${GREEN}✅ Nginx服务运行正常${NC}"
                if nginx -t; then
                    echo -e "${GREEN}✅ Nginx配置语法正确${NC}"
                else
                    echo -e "${RED}❌ Nginx配置语法错误${NC}"
                fi
            else
                echo -e "${RED}❌ Nginx服务未运行${NC}"
            fi
            ;;
        4)
            echo -e "${GREEN}🌐 测试外部访问...${NC}"

            echo -e "${CYAN}=== 测试Love网站访问 (新域名) ===${NC}"
            if curl -s -I "https://$DOMAIN/" | head -1 | grep -q "200"; then
                echo -e "${GREEN}✅ Love网站首页访问正常${NC}"
            else
                echo -e "${RED}❌ Love网站首页访问失败${NC}"
                echo -e "${YELLOW}   请检查宝塔面板反向代理配置${NC}"
            fi

            echo -e "${CYAN}=== 测试Love API访问 ===${NC}"
            if curl -s "https://$DOMAIN/api/health" | grep -q "ok\|healthy\|success" 2>/dev/null; then
                echo -e "${GREEN}✅ Love API访问正常${NC}"
            else
                echo -e "${YELLOW}⚠️  Love API访问可能有问题${NC}"
                echo -e "${CYAN}   检查项目:${NC}"
                echo -e "   1. Love服务是否运行在端口$LOVE_PORT"
                echo -e "   2. 宝塔面板反向代理是否配置正确"
            fi

            echo -e "${CYAN}=== 测试Love页面访问 ===${NC}"
            local pages=("together-days" "anniversary" "meetings" "memorial")
            for page in "${pages[@]}"; do
                if curl -s -I "https://$DOMAIN/$page" | head -1 | grep -q "200"; then
                    echo -e "${GREEN}✅ $page 访问正常${NC}"
                else
                    echo -e "${YELLOW}⚠️  $page 访问可能有问题${NC}"
                fi
            done

            echo -e "\n${CYAN}=== 本地服务检查 ===${NC}"
            if curl -s "http://localhost:$LOVE_PORT/api/health" | grep -q "ok\|healthy\|success" 2>/dev/null; then
                echo -e "${GREEN}✅ 本地服务运行正常${NC}"
            else
                echo -e "${RED}❌ 本地服务无响应，请检查Love服务状态${NC}"
            fi
            ;;
        5)
            echo -e "${GREEN}🔒 SSL证书管理...${NC}"
            echo -e "${CYAN}ℹ️  现在使用宝塔面板管理SSL证书${NC}"
            echo -e "${YELLOW}请在宝塔面板中进行以下操作:${NC}"
            echo -e "   1. 进入站点设置 → SSL"
            echo -e "   2. 申请或续期Let's Encrypt证书"
            echo -e "   3. 开启强制HTTPS"

            # 检查证书状态
            if [ -f "$BT_SSL_DIR/fullchain.pem" ]; then
                local cert_expire=$(openssl x509 -in "$BT_SSL_DIR/fullchain.pem" -noout -enddate 2>/dev/null | cut -d= -f2 || echo "未知")
                echo -e "${GREEN}✅ 当前证书到期时间: $cert_expire${NC}"
            fi
            ;;
        6)
            echo -e "${GREEN}🔍 检查宝塔配置...${NC}"
            echo -e "${CYAN}宝塔站点配置检查:${NC}"
            if [ -f "$BT_NGINX_CONF" ]; then
                echo -e "${GREEN}✅ 宝塔站点配置存在${NC}"
                echo -e "${CYAN}配置文件位置: $BT_NGINX_CONF${NC}"
            else
                echo -e "${RED}❌ 宝塔站点配置不存在${NC}"
            fi

            echo -e "${CYAN}SSL证书检查:${NC}"
            if [ -f "$BT_SSL_DIR/fullchain.pem" ]; then
                echo -e "${GREEN}✅ SSL证书存在${NC}"
                openssl x509 -in "$BT_SSL_DIR/fullchain.pem" -noout -dates 2>/dev/null || echo "证书信息读取失败"
            else
                echo -e "${RED}❌ SSL证书不存在${NC}"
            fi
            ;;
        7)
            return
            ;;
        *)
            echo -e "${RED}❌ 无效选择${NC}"
            ;;
    esac
}

# 监控管理
monitoring() {
    echo -e "${CYAN}=== 📊 监控管理 ===${NC}"

    echo -e "${WHITE}选择操作:${NC}"
    echo -e "${GREEN}1)${NC} 实时监控"
    echo -e "${GREEN}2)${NC} 性能检查"
    echo -e "${GREEN}3)${NC} 错误日志分析"
    echo -e "${GREEN}4)${NC} 网络连接测试"
    echo -e "${GREEN}5)${NC} 返回主菜单"

    read -p "请选择 (1-5): " choice

    case $choice in
        1)
            echo -e "${GREEN}📊 实时监控 (按Ctrl+C退出)...${NC}"
            watch -n 2 "
                echo '=== Love Site 实时状态 ==='
                echo '后端进程:' \$(pgrep -f 'node server.js' | wc -l)
                echo 'Nginx状态:' \$(systemctl is-active nginx)
                echo '端口3001:' \$(ss -tuln | grep :3001 | wc -l)
                echo '端口443:' \$(ss -tuln | grep :443 | wc -l)
                echo '内存使用:' \$(free -h | grep Mem | awk '{print \$3\"/\"\$2}')
                echo '磁盘使用:' \$(df -h / | tail -1 | awk '{print \$5}')
                echo '最新日志:'
                tail -3 $LOVE_LOG_FILE 2>/dev/null || echo '无日志'
            "
            ;;
        2)
            echo -e "${GREEN}🔍 性能检查...${NC}"
            echo -e "${CYAN}系统负载:${NC}"
            uptime
            echo -e "\n${CYAN}内存使用:${NC}"
            free -h
            echo -e "\n${CYAN}磁盘使用:${NC}"
            df -h /
            echo -e "\n${CYAN}网络连接:${NC}"
            ss -tuln | grep -E ":(443|3001)"
            ;;
        3)
            echo -e "${GREEN}🔍 错误日志分析...${NC}"
            echo -e "${CYAN}后端错误 (最近20行):${NC}"
            grep -i error "$LOVE_LOG_FILE" | tail -20 2>/dev/null || echo "无错误日志"
            echo -e "\n${CYAN}Nginx错误 (最近10行):${NC}"
            sudo grep -i error /var/log/nginx/error.log | tail -10 2>/dev/null || echo "无错误日志"
            ;;
        4)
            echo -e "${GREEN}🌐 网络连接测试...${NC}"
            echo -e "${CYAN}本地API测试:${NC}"
            curl -s http://localhost:$LOVE_PORT/api/health | jq . 2>/dev/null || echo "API测试失败"
            echo -e "\n${CYAN}新域名生产环境测试:${NC}"
            curl -s https://$DOMAIN/api/health | jq . 2>/dev/null || echo "新域名测试失败"
            echo -e "\n${CYAN}宝塔面板反向代理测试:${NC}"
            if curl -s -I "https://$DOMAIN/" | head -1 | grep -q "200"; then
                echo -e "${GREEN}✅ 宝塔反向代理工作正常${NC}"
            else
                echo -e "${RED}❌ 宝塔反向代理可能有问题${NC}"
            fi
            ;;
        5)
            return
            ;;
        *)
            echo -e "${RED}❌ 无效选择${NC}"
            ;;
    esac
}

# 故障排除
troubleshooting() {
    echo -e "${CYAN}=== 🔧 故障排除 ===${NC}"

    echo -e "${WHITE}选择操作:${NC}"
    echo -e "${GREEN}1)${NC} 修复后端服务"
    echo -e "${GREEN}2)${NC} 修复Nginx配置"
    echo -e "${GREEN}3)${NC} 修复数据库"
    echo -e "${GREEN}4)${NC} 修复SSL证书"
    echo -e "${GREEN}5)${NC} 清理临时文件"
    echo -e "${GREEN}6)${NC} 返回主菜单"

    read -p "请选择 (1-6): " choice

    case $choice in
        1)
            echo -e "${GREEN}🔧 修复后端服务...${NC}"
            # 杀死所有node进程
            pkill -f "node server.js" 2>/dev/null || true
            sleep 2
            # 清理PID文件
            rm -f "$LOVE_PID_FILE"
            # 重新启动
            cd "$LOVE_DIR"
            ./start-backend.sh
            ;;
        2)
            echo -e "${GREEN}🔧 检查宝塔面板配置...${NC}"
            echo -e "${CYAN}ℹ️  现在使用宝塔面板管理Nginx配置${NC}"

            if [ -f "$BT_NGINX_CONF" ]; then
                echo -e "${GREEN}✅ 宝塔站点配置存在${NC}"

                # 检查反向代理配置
                if grep -q "proxy_pass.*127.0.0.1:$LOVE_PORT" "$BT_NGINX_CONF" 2>/dev/null; then
                    echo -e "${GREEN}✅ 反向代理配置正确${NC}"
                else
                    echo -e "${YELLOW}⚠️  反向代理配置可能有问题${NC}"
                    echo -e "${CYAN}建议操作:${NC}"
                    echo -e "   1. 登录宝塔面板"
                    echo -e "   2. 进入站点设置 → 反向代理"
                    echo -e "   3. 确认目标URL为: http://127.0.0.1:$LOVE_PORT"
                    echo -e "   4. 确认代理目录为: /"
                fi
            else
                echo -e "${RED}❌ 宝塔站点配置不存在${NC}"
                echo -e "${CYAN}请在宝塔面板中创建站点: $DOMAIN${NC}"
            fi
            ;;
        3)
            echo -e "${GREEN}🔧 修复数据库...${NC}"
            if [ -f "$LOVE_DIR/love_messages.db" ]; then
                echo "检查数据库完整性..."
                sqlite3 "$LOVE_DIR/love_messages.db" "PRAGMA integrity_check;"
            else
                echo "重新创建数据库..."
                cd "$LOVE_DIR"
                node -e "
                    const sqlite3 = require('sqlite3').verbose();
                    const db = new sqlite3.Database('love_messages.db');
                    db.serialize(() => {
                        db.run(\`CREATE TABLE IF NOT EXISTS love_messages (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            author TEXT NOT NULL CHECK(author IN ('Yu', 'Wang', 'Other')),
                            content TEXT NOT NULL,
                            created_at INTEGER NOT NULL,
                            updated_at INTEGER NOT NULL,
                            ip TEXT DEFAULT ''
                        )\`);
                        db.run(\`CREATE INDEX IF NOT EXISTS idx_created_at ON love_messages(created_at)\`);
                    });
                    db.close();
                    console.log('数据库已重新创建');
                "
            fi
            ;;
        4)
            echo -e "${GREEN}🔧 SSL证书管理...${NC}"
            echo -e "${CYAN}ℹ️  现在使用宝塔面板管理SSL证书${NC}"
            echo -e "${YELLOW}请在宝塔面板中进行以下操作:${NC}"
            echo -e "   1. 进入站点设置 → SSL"
            echo -e "   2. 重新申请Let's Encrypt证书"
            echo -e "   3. 开启强制HTTPS"
            echo -e "   4. 检查证书自动续期设置"

            # 检查当前证书状态
            if [ -f "$BT_SSL_DIR/fullchain.pem" ]; then
                echo -e "\n${CYAN}当前证书信息:${NC}"
                openssl x509 -in "$BT_SSL_DIR/fullchain.pem" -noout -dates 2>/dev/null || echo "证书信息读取失败"
            else
                echo -e "\n${RED}❌ 未找到SSL证书文件${NC}"
            fi
            ;;
        5)
            echo -e "${GREEN}🧹 清理临时文件...${NC}"
            cd "$LOVE_DIR"
            rm -f *.tmp *.log.* backend.pid.* 2>/dev/null || true
            echo "临时文件已清理"
            ;;
        6)
            return
            ;;
        *)
            echo -e "${RED}❌ 无效选择${NC}"
            ;;
    esac
}

# 服务管理
service_management() {
    echo -e "${CYAN}=== ⚙️ 服务管理 ===${NC}"

    echo -e "${WHITE}选择操作:${NC}"
    echo -e "${GREEN}1)${NC} 查看服务状态"
    echo -e "${GREEN}2)${NC} 启用开机自启"
    echo -e "${GREEN}3)${NC} 禁用开机自启"
    echo -e "${GREEN}4)${NC} 查看服务日志"
    echo -e "${GREEN}5)${NC} 重新创建服务"
    echo -e "${GREEN}6)${NC} 删除系统服务"
    echo -e "${GREEN}7)${NC} 返回主菜单"

    read -p "请选择 (1-7): " choice

    case $choice in
        1)
            echo -e "${GREEN}📊 Love Site服务状态:${NC}"
            if systemctl list-unit-files | grep -q "love-site.service"; then
                sudo systemctl status love-site --no-pager
            else
                echo "Love Site系统服务未创建"
            fi
            ;;
        2)
            echo -e "${GREEN}🚀 启用开机自启...${NC}"
            if systemctl list-unit-files | grep -q "love-site.service"; then
                sudo systemctl enable love-site
                echo "✅ 开机自启已启用"
            else
                echo "❌ 请先创建系统服务"
            fi
            ;;
        3)
            echo -e "${YELLOW}🛑 禁用开机自启...${NC}"
            if systemctl list-unit-files | grep -q "love-site.service"; then
                sudo systemctl disable love-site
                echo "✅ 开机自启已禁用"
            else
                echo "❌ 系统服务不存在"
            fi
            ;;
        4)
            echo -e "${GREEN}📝 查看服务日志...${NC}"
            if systemctl list-unit-files | grep -q "love-site.service"; then
                echo "最近50行日志:"
                sudo journalctl -u love-site -n 50 --no-pager
                echo -e "\n${BLUE}💡 实时查看日志: sudo journalctl -u love-site -f${NC}"
            else
                echo "❌ 系统服务不存在"
            fi
            ;;
        5)
            echo -e "${GREEN}🔧 重新创建服务...${NC}"
            # 停止并删除现有服务
            if systemctl list-unit-files | grep -q "love-site.service"; then
                sudo systemctl stop love-site 2>/dev/null || true
                sudo systemctl disable love-site 2>/dev/null || true
                sudo rm -f /etc/systemd/system/love-site.service
                sudo systemctl daemon-reload
            fi
            # 重新创建
            create_systemd_service
            ;;
        6)
            read -p "确定要删除Love Site系统服务吗？(输入 'YES' 确认): " confirm
            if [ "$confirm" = "YES" ]; then
                echo -e "${YELLOW}🗑️ 删除系统服务...${NC}"
                sudo systemctl stop love-site 2>/dev/null || true
                sudo systemctl disable love-site 2>/dev/null || true
                sudo rm -f /etc/systemd/system/love-site.service
                sudo systemctl daemon-reload
                echo "✅ 系统服务已删除"
            else
                echo "操作已取消"
            fi
            ;;
        7)
            return
            ;;
        *)
            echo -e "${RED}❌ 无效选择${NC}"
            ;;
    esac
}

# 项目管理
project_management() {
    echo -e "${CYAN}=== 📁 项目管理 ===${NC}"

    echo -e "${WHITE}选择操作:${NC}"
    echo -e "${GREEN}1)${NC} 清理多余脚本"
    echo -e "${GREEN}2)${NC} 清理测试文件"
    echo -e "${GREEN}3)${NC} 清理临时文件"
    echo -e "${GREEN}4)${NC} 项目结构优化"
    echo -e "${GREEN}5)${NC} 生成测试数据"
    echo -e "${GREEN}6)${NC} 查看项目信息"
    echo -e "${GREEN}7)${NC} 返回主菜单"

    read -p "请选择 (1-7): " choice

    case $choice in
        1)
            echo -e "${GREEN}🧹 清理多余脚本...${NC}"
            cd "$LOVE_DIR"

            # 列出要删除的脚本
            echo "将删除以下脚本文件:"
            echo "- start-backend.sh (功能已集成到manage.sh)"
            echo "- quick-start.sh (功能已集成到manage.sh)"
            echo "- deploy-love-backend.sh (功能已集成到manage.sh)"
            echo "- setup-nginx.sh (功能已集成到manage.sh)"
            echo "- fix-nginx.sh (功能已集成到manage.sh)"
            echo "- cleanup-old-files.sh (功能已集成到manage.sh)"
            echo ""

            read -p "确定要删除这些脚本吗？(输入 'YES' 确认): " confirm
            if [ "$confirm" = "YES" ]; then
                rm -f start-backend.sh quick-start.sh deploy-love-backend.sh setup-nginx.sh fix-nginx.sh cleanup-old-files.sh 2>/dev/null || true
                echo -e "${GREEN}✅ 多余脚本已清理${NC}"
            else
                echo -e "${YELLOW}操作已取消${NC}"
            fi
            ;;
        2)
            echo -e "${GREEN}🧹 清理测试文件...${NC}"
            cd "$LOVE_DIR"

            echo "将删除以下测试文件:"
            ls -1 test-*.html debug-*.html 2>/dev/null || echo "没有找到测试文件"
            echo ""

            read -p "确定要删除测试文件吗？(输入 'YES' 确认): " confirm
            if [ "$confirm" = "YES" ]; then
                rm -f test-*.html debug-*.html 2>/dev/null || true
                echo -e "${GREEN}✅ 测试文件已清理${NC}"
            else
                echo -e "${YELLOW}操作已取消${NC}"
            fi
            ;;
        3)
            echo -e "${GREEN}🧹 清理临时文件...${NC}"
            cd "$LOVE_DIR"

            # 清理各种临时文件
            rm -f *.tmp *.log.* backend.pid.* temp_*.js *.bak 2>/dev/null || true
            rm -f migrate-*.js create-test-data.js 2>/dev/null || true

            echo -e "${GREEN}✅ 临时文件已清理${NC}"
            ;;
        4)
            echo -e "${GREEN}📁 项目结构优化...${NC}"
            cd "$LOVE_DIR"

            # 确保目录结构正确
            mkdir -p "$DATA_DIR" "$BACKUP_DIR"

            # 移动旧的备份文件到新位置
            if [ -d "backups" ] && [ "$BACKUP_DIR" != "$LOVE_DIR/backups" ]; then
                echo "迁移备份文件到data/backups/..."
                mv backups/* "$BACKUP_DIR/" 2>/dev/null || true
                rmdir backups 2>/dev/null || true
            fi

            echo -e "${GREEN}✅ 项目结构已优化${NC}"
            echo "目录结构:"
            echo "├── data/                 # 数据文件目录"
            echo "│   ├── love_messages.db  # 主数据库"
            echo "│   └── backups/          # 备份文件"
            echo "├── manage.sh             # 统一管理脚本"
            echo "├── server.js             # 后端服务"
            echo "├── index.html            # 前端页面"
            echo "└── ..."
            ;;
        5)
            echo -e "${GREEN}📊 生成测试数据...${NC}"
            if [ -f "$LOVE_DB_FILE" ]; then
                echo "生成测试留言数据..."

                # 创建临时脚本生成测试数据
                cat > /tmp/generate_test_data.js << 'EOF'
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = process.argv[2];
const db = new sqlite3.Database(dbPath);

const testMessages = [
    { author: 'Yu', content: '测试留言 - 今天天气真好 ☀️' },
    { author: 'Wang', content: '测试留言 - 同意，很适合出去走走 🚶‍♀️' },
    { author: 'Yu', content: '测试留言 - 那我们一起去公园吧 💕' },
    { author: 'Wang', content: '测试留言 - 好的，我准备一下 🎒' },
    { author: 'Other', content: '测试留言 - 祝你们玩得开心！ 🎉' }
];

const now = Math.floor(Date.now() / 1000);

// 检查表结构
db.get("SELECT name FROM sqlite_master WHERE type='table' AND name='love_messages_new'", (err, row) => {
    if (row) {
        // 使用新表结构
        testMessages.forEach((msg, index) => {
            const timestamp = now + index * 60; // 每条消息间隔1分钟
            const beijingTime = new Date(timestamp * 1000);
            beijingTime.setHours(beijingTime.getHours() + 8); // 转换为北京时间
            const beijingDate = beijingTime.toISOString().split('T')[0];
            const beijingDateTime = beijingTime.toISOString().replace('T', ' ').split('.')[0];

            db.run(
                `INSERT INTO love_messages_new
                (author, content, created_timestamp, updated_timestamp, beijing_date, beijing_datetime, client_ip, status, version)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [msg.author, msg.content, timestamp, timestamp, beijingDate, beijingDateTime, '127.0.0.1', 'active', 1]
            );
        });
    } else {
        // 使用旧表结构
        testMessages.forEach((msg, index) => {
            const timestamp = now + index * 60;
            db.run(
                'INSERT INTO love_messages (author, content, created_at, updated_at, ip) VALUES (?, ?, ?, ?, ?)',
                [msg.author, msg.content, timestamp, timestamp, '127.0.0.1']
            );
        });
    }

    console.log('✅ 测试数据生成完成');
    db.close();
});
EOF

                node /tmp/generate_test_data.js "$LOVE_DB_FILE"
                rm -f /tmp/generate_test_data.js
            else
                echo -e "${RED}❌ 数据库文件不存在${NC}"
            fi
            ;;
        6)
            echo -e "${GREEN}📊 项目信息:${NC}"
            cd "$LOVE_DIR"

            echo "项目名称: Love Site"
            echo "项目路径: $LOVE_DIR"
            echo "数据目录: $DATA_DIR"
            echo "备份目录: $BACKUP_DIR"
            echo ""

            echo "文件统计:"
            echo "- HTML文件: $(find . -name "*.html" -not -path "./node_modules/*" | wc -l) 个"
            echo "- CSS文件: $(find . -name "*.css" -not -path "./node_modules/*" | wc -l) 个"
            echo "- JS文件: $(find . -name "*.js" -not -path "./node_modules/*" | wc -l) 个"
            echo "- 脚本文件: $(find . -name "*.sh" | wc -l) 个"
            echo ""

            echo "目录大小:"
            echo "- 项目总大小: $(du -sh . | cut -f1)"
            echo "- 数据目录: $(du -sh "$DATA_DIR" 2>/dev/null | cut -f1 || echo "0B")"
            echo "- node_modules: $(du -sh node_modules 2>/dev/null | cut -f1 || echo "0B")"
            ;;
        7)
            return
            ;;
        *)
            echo -e "${RED}❌ 无效选择${NC}"
            ;;
    esac
}

# 主菜单
show_menu() {
    clear
    show_title

    echo -e "${WHITE}请选择操作:${NC}"
    echo -e "${GREEN} 1)${NC} 📊 查看状态"
    echo -e "${GREEN} 2)${NC} 🚀 启动服务"
    echo -e "${GREEN} 3)${NC} 🛑 停止服务"
    echo -e "${GREEN} 4)${NC} 🔄 重启服务"
    echo -e "${GREEN} 5)${NC} 📝 查看日志"
    echo -e "${GREEN} 6)${NC} ⚙️ 服务管理"
    echo -e "${GREEN} 7)${NC} 💾 数据库管理"
    echo -e "${GREEN} 8)${NC} 🚀 部署管理"
    echo -e "${GREEN} 9)${NC} 📊 监控管理"
    echo -e "${GREEN}10)${NC} 🔧 故障排除"
    echo -e "${GREEN}11)${NC} 📁 项目管理"
    echo -e "${GREEN}12)${NC} ❓ 帮助信息"
    echo -e "${GREEN} 0)${NC} 🚪 退出"
    echo
}

# 帮助信息
show_help() {
    echo -e "${CYAN}=== 💡 Love Site Manager 帮助 ===${NC}"
    echo
    echo -e "${WHITE}常用命令:${NC}"
    echo -e "${GREEN}./manage.sh${NC}                    - 启动管理界面"
    echo -e "${GREEN}./manage.sh status${NC}             - 快速查看状态"
    echo -e "${GREEN}./manage.sh start${NC}              - 快速启动服务"
    echo -e "${GREEN}./manage.sh stop${NC}               - 快速停止服务"
    echo -e "${GREEN}./manage.sh restart${NC}            - 快速重启服务"
    echo -e "${GREEN}./manage.sh logs${NC}               - 快速查看日志"
    echo
    echo -e "${WHITE}文件位置:${NC}"
    echo -e "${CYAN}项目目录:${NC} $LOVE_DIR"
    echo -e "${CYAN}宝塔配置:${NC} $BT_NGINX_CONF"
    echo -e "${CYAN}数据库:${NC} $LOVE_DIR/data/love_messages.db"
    echo -e "${CYAN}日志文件:${NC} $LOVE_LOG_FILE"
    echo
    echo -e "${WHITE}访问地址 (新域名):${NC}"
    echo -e "${CYAN}网站首页:${NC} https://$DOMAIN/"
    echo -e "${CYAN}在一起的日子:${NC} https://$DOMAIN/together-days"
    echo -e "${CYAN}纪念日:${NC} https://$DOMAIN/anniversary"
    echo -e "${CYAN}相遇记录:${NC} https://$DOMAIN/meetings"
    echo -e "${CYAN}纪念页面:${NC} https://$DOMAIN/memorial"
    echo -e "${CYAN}API接口:${NC} https://$DOMAIN/api/"
    echo -e "${CYAN}管理界面:${NC} ./manage.sh (服务器内部)"
    echo
    echo -e "${WHITE}宝塔面板配置:${NC}"
    echo -e "${CYAN}域名:${NC} $DOMAIN"
    echo -e "${CYAN}反向代理:${NC} http://127.0.0.1:$LOVE_PORT"
    echo -e "${CYAN}SSL证书:${NC} 通过宝塔面板管理"
    echo
    echo -e "${WHITE}故障排除:${NC}"
    echo -e "${CYAN}1.${NC} 如果服务无法启动，检查端口是否被占用"
    echo -e "${CYAN}2.${NC} 如果网站无法访问，检查Nginx配置和SSL证书"
    echo -e "${CYAN}3.${NC} 如果数据库出错，尝试备份后重新创建"
    echo -e "${CYAN}4.${NC} 查看日志文件获取详细错误信息"
    echo
}

# 主程序
main() {
    # 检查是否在正确的目录
    if [ ! -f "$LOVE_DIR/server.js" ]; then
        echo -e "${RED}❌ 错误: 找不到Love Site项目文件${NC}"
        echo -e "${YELLOW}期望路径: $LOVE_DIR/server.js${NC}"
        echo -e "${YELLOW}请确保脚本在Love项目目录中运行${NC}"
        exit 1
    fi

    # 处理命令行参数
    case "${1:-}" in
        "status")
            show_status
            exit 0
            ;;
        "start")
            start_services
            exit 0
            ;;
        "stop")
            stop_services
            exit 0
            ;;
        "restart")
            restart_services
            exit 0
            ;;
        "logs")
            view_logs
            exit 0
            ;;
        "help"|"-h"|"--help")
            show_help
            exit 0
            ;;
    esac

    # 交互式菜单
    while true; do
        show_menu
        read -p "请选择 (0-12): " choice

        case $choice in
            1)
                show_status
                read -p "按回车键继续..."
                ;;
            2)
                start_services
                read -p "按回车键继续..."
                ;;
            3)
                stop_services
                read -p "按回车键继续..."
                ;;
            4)
                restart_services
                read -p "按回车键继续..."
                ;;
            5)
                view_logs
                read -p "按回车键继续..."
                ;;
            6)
                service_management
                read -p "按回车键继续..."
                ;;
            7)
                manage_database
                read -p "按回车键继续..."
                ;;
            8)
                deploy_management
                read -p "按回车键继续..."
                ;;
            9)
                monitoring
                read -p "按回车键继续..."
                ;;
            10)
                troubleshooting
                read -p "按回车键继续..."
                ;;
            11)
                project_management
                read -p "按回车键继续..."
                ;;
            12)
                show_help
                read -p "按回车键继续..."
                ;;
            0)
                echo -e "${GREEN}👋 再见！感谢使用 Love Site Manager${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}❌ 无效选择，请重新输入${NC}"
                sleep 1
                ;;
        esac
    done
}

# 运行主程序
main "$@"
