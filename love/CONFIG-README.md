# 💕 Love Website 配置系统说明

## 📋 概述

Love Website 采用统一的配置管理系统，避免硬编码，便于维护和部署。现在支持域名配置、环境变量管理、服务器配置等功能。

## 🏗️ 配置系统架构

```
Love Website 配置系统
├── config.js              # 前端配置文件（浏览器端）
├── server-config.js       # 服务器端配置文件
├── .env                   # 环境变量配置文件
├── .env.example           # 环境变量配置模板
├── config-manager.sh      # 配置管理脚本（新增）
├── dynamic-styles.js      # 动态样式管理器
├── manage-config.sh       # 原有配置管理脚本
├── update-config.js       # 配置更新工具
└── CONFIG-README.md       # 本说明文件
```

## 🆕 新增功能

### 1. 域名统一配置
现在可以在配置文件中统一管理域名，避免硬编码：

**当前域名**: `love.yuh.cool`

**前端配置 (config.js)**：
```javascript
DOMAIN: {
    PRODUCTION: 'love.yuh.cool',      // 生产环境域名
    DEVELOPMENT: 'localhost:1314',    // 开发环境域名
    get current() { /* 自动检测 */ }, // 当前环境域名
    get baseUrl() { /* 完整URL */ },  // 包含协议的完整URL
    get apiUrl() { /* API URL */ }    // API完整地址
}
```

**服务器配置 (server-config.js)**：
```javascript
DOMAIN: {
    PRODUCTION: 'love.yuh.cool',
    DEVELOPMENT: 'localhost',
    get current() { /* 根据NODE_ENV自动选择 */ },
    get baseUrl() { /* 包含协议和端口 */ }
}
```

### 2. 环境变量支持
通过 `.env` 文件管理环境配置：

```bash
# 域名配置
LOVE_DOMAIN_PRODUCTION=love.yuh.cool

# 服务器配置
LOVE_PORT=1314
LOVE_HOST=0.0.0.0

# 数据库配置
LOVE_DB_PATH=./data/love_messages.db
```

## 📁 核心文件说明

### 1. `config.js` - 主配置文件

这是整个配置系统的核心，包含：

- **API配置**: 基础URL和端点定义
- **路径配置**: 字体、背景、脚本等静态资源路径
- **字体配置**: 所有自定义字体的详细信息
- **视频配置**: 背景视频资源配置
- **网站信息**: 基本的网站元数据
- **工具函数**: 用于获取URL和管理资源的辅助函数

### 2. `dynamic-styles.js` - 动态样式管理器

自动处理：
- 根据配置动态生成字体CSS
- 更新视频源路径
- 更新脚本源路径
- 开发模式下显示配置状态

### 3. `manage-config.sh` - 配置管理脚本

提供命令行界面来：
- 查看当前配置
- 修改各种配置项
- 备份和恢复配置
- 验证配置文件语法
- 重启服务

## 🚀 使用方法

### 基本使用

1. **在HTML中引入配置**：
```html
<!-- 必须在其他脚本之前加载 -->
<script src="/config.js"></script>
<script src="/dynamic-styles.js"></script>
```

2. **在JavaScript中使用配置**：
```javascript
// 获取API URL
const apiUrl = CONFIG.API.BASE_URL;
const messagesUrl = CONFIG.UTILS.getApiUrl(CONFIG.API.ENDPOINTS.MESSAGES);

// 获取字体URL
const fontUrl = CONFIG.FONTS.COURGETTE.url;

// 获取视频URL
const videoUrl = CONFIG.VIDEOS.FLOWER_BACKGROUND.encodedUrl;
```

3. **在CSS中使用（通过动态样式）**：
动态样式管理器会自动生成字体CSS，无需手动编写。

### 配置管理

1. **使用管理脚本**：
```bash
./manage-config.sh
```

2. **手动编辑配置文件**：
直接编辑 `config.js` 文件，然后重启服务。

## 🔧 配置项详解

### API配置
```javascript
API: {
    BASE_URL: '/api',
    ENDPOINTS: {
        MESSAGES: '/messages',
        HEALTH: '/health',
        TOGETHER_DAYS: '/together-days'
    }
}
```

### 路径配置
```javascript
PATHS: {
    FONTS: '/fonts',
    BACKGROUND: '/background',
    SCRIPTS: {
        ROMANTIC_QUOTES: '/romantic-quotes.js',
        MODERN_QUOTES_DATA: '/modern-quotes-data.js',
        MAIN_SCRIPT: '/script.js'
    }
}
```

### 字体配置
```javascript
FONTS: {
    COURGETTE: {
        name: 'Courgette',
        file: 'Courgette-Regular.ttf',
        get url() { return `${CONFIG.PATHS.FONTS}/${this.file}`; }
    }
}
```

## 🎯 优势

### 1. **集中管理**
- 所有配置集中在一个文件中
- 修改路径只需要改一个地方
- 避免了分散在各个文件中的硬编码路径

### 2. **动态更新**
- 支持运行时配置更新
- 动态样式管理器自动应用更改
- 无需手动更新每个文件

### 3. **开发友好**
- 提供配置管理脚本
- 支持配置备份和恢复
- 开发模式下显示配置状态

### 4. **类型安全**
- 使用getter函数确保URL正确性
- 配置验证功能
- 错误处理和回退机制

## 📝 迁移指南

### 从旧系统迁移

1. **备份现有文件**：
```bash
./manage-config.sh  # 选择备份选项
```

2. **更新HTML文件**：
在 `<head>` 中添加配置文件引用：
```html
<script src="/config.js"></script>
<script src="/dynamic-styles.js"></script>
```

3. **更新JavaScript文件**：
将硬编码的路径替换为配置引用：
```javascript
// 旧方式
const API_BASE_URL = '/api';

// 新方式
const API_BASE_URL = CONFIG.API.BASE_URL;
```

4. **更新CSS文件**：
字体路径现在由动态样式管理器处理，可以移除手动的 `@font-face` 规则。

## 🛠️ 故障排除

### 常见问题

1. **配置文件未加载**：
   - 确保 `config.js` 在其他脚本之前加载
   - 检查服务器是否正确提供配置文件

2. **字体未显示**：
   - 检查 `dynamic-styles.js` 是否正确加载
   - 查看浏览器控制台是否有错误信息

3. **API请求失败**：
   - 验证 `CONFIG.API.BASE_URL` 是否正确
   - 检查端点配置是否匹配服务器路由

### 调试工具

1. **浏览器控制台**：
```javascript
// 查看完整配置
console.log(CONFIG);

// 查看特定配置
console.log(CONFIG.API.BASE_URL);

// 重新应用动态样式
DynamicStyles.initialize();
```

2. **配置验证**：
```bash
./manage-config.sh  # 选择验证选项
```

## 🔄 更新日志

### v2.0.0 (当前版本)
- ✅ 创建统一配置系统
- ✅ 实现动态样式管理
- ✅ 添加配置管理脚本
- ✅ 支持配置备份和恢复

### 未来计划
- 🔄 支持环境变量配置
- 🔄 添加配置热重载
- 🔄 实现配置版本管理
- 🔄 添加更多配置验证规则

## 📞 支持

如果在使用配置系统时遇到问题，请：

1. 查看本文档的故障排除部分
2. 使用配置管理脚本进行诊断
3. 检查浏览器控制台的错误信息
4. 必要时恢复到备份配置

---

*Love Website Configuration System v2.0.0*
