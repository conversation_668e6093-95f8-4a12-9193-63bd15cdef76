<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>星星话语测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
        }
        .test-section h2 {
            color: #555;
            margin-bottom: 15px;
        }
        .quote-display {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);
            padding: 25px;
            border-radius: 12px;
            margin: 15px 0;
            border-left: 4px solid #3b82f6;
            font-size: 16px;
            line-height: 1.8;
            min-height: 80px;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.1);
            transition: all 0.3s ease;
        }

        .quote-display:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
        }
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .category-btn {
            background: #764ba2;
        }
        .category-btn:hover {
            background: #6a4190;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>💫 星星话语测试页面</h1>
        
        <div class="test-section">
            <h2>📊 诗词库统计</h2>
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-number" id="totalQuotes">0</div>
                    <div class="stat-label">总诗词数量</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="confessionQuotes">0</div>
                    <div class="stat-label">表白类</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="missingQuotes">0</div>
                    <div class="stat-label">思念类</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="promiseQuotes">0</div>
                    <div class="stat-label">承诺类</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="sweetQuotes">0</div>
                    <div class="stat-label">甜蜜类</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🎲 随机诗词测试</h2>
            <button class="btn" onclick="testRandomQuote()">获取随机诗词</button>
            <div class="quote-display" id="randomQuote">点击按钮获取随机诗词...</div>
        </div>

        <div class="test-section">
            <h2>📝 分类诗词测试</h2>
            <div>
                <button class="btn category-btn" onclick="testCategoryQuote('confession')">表白类</button>
                <button class="btn category-btn" onclick="testCategoryQuote('missing')">思念类</button>
                <button class="btn category-btn" onclick="testCategoryQuote('promise')">承诺类</button>
                <button class="btn category-btn" onclick="testCategoryQuote('sweet')">甜蜜类</button>
            </div>
            <div class="quote-display" id="categoryQuote">点击分类按钮获取对应类别的诗词...</div>
        </div>

        <div class="test-section">
            <h2>🔄 连续测试</h2>
            <button class="btn" onclick="startContinuousTest()">开始连续测试</button>
            <button class="btn" onclick="stopContinuousTest()">停止测试</button>
            <div class="quote-display" id="continuousQuote">连续测试将在这里显示诗词...</div>
        </div>
    </div>

    <script src="/romantic-quotes.js"></script>
    <script>
        let continuousTestInterval = null;

        // 初始化统计信息
        function initStats() {
            document.getElementById('totalQuotes').textContent = romanticQuotes.length;
            document.getElementById('confessionQuotes').textContent = categorizedQuotes.confession ? categorizedQuotes.confession.length : 0;
            document.getElementById('missingQuotes').textContent = categorizedQuotes.missing ? categorizedQuotes.missing.length : 0;
            document.getElementById('promiseQuotes').textContent = categorizedQuotes.promise ? categorizedQuotes.promise.length : 0;
            document.getElementById('sweetQuotes').textContent = categorizedQuotes.sweet ? categorizedQuotes.sweet.length : 0;
        }

        // 测试随机诗词
        function testRandomQuote() {
            const quote = getRandomQuote();
            const displayText = formatQuoteDisplay(quote);
            document.getElementById('randomQuote').innerHTML = displayText;
        }

        // 测试分类诗词
        function testCategoryQuote(category) {
            const quote = getRandomQuoteByCategory(category);
            const displayText = formatQuoteDisplay(quote, category);
            document.getElementById('categoryQuote').innerHTML = displayText;
        }

        // 格式化诗词显示
        function formatQuoteDisplay(quote, category = '') {
            if (typeof quote === 'object' && quote.text) {
                return `
                    ${category ? `<strong>【${category}】</strong><br>` : ''}
                    <div style="font-size: 16px; margin: 10px 0; color: #333;">${quote.text}</div>
                    <div style="font-size: 14px; color: #666; margin: 5px 0;">—— ${quote.author}</div>
                    <div style="font-size: 12px; color: #999;">${quote.work}</div>
                `;
            } else {
                return `${category ? `【${category}】` : ''}${quote}`;
            }
        }

        // 开始连续测试
        function startContinuousTest() {
            if (continuousTestInterval) {
                clearInterval(continuousTestInterval);
            }
            
            const categories = ['confession', 'missing', 'promise', 'sweet'];
            let index = 0;
            
            continuousTestInterval = setInterval(() => {
                const category = categories[index % categories.length];
                const quote = getRandomQuoteByCategory(category);
                const displayText = formatQuoteDisplay(quote, category);
                document.getElementById('continuousQuote').innerHTML = displayText;
                index++;
            }, 3000);
        }

        // 停止连续测试
        function stopContinuousTest() {
            if (continuousTestInterval) {
                clearInterval(continuousTestInterval);
                continuousTestInterval = null;
                document.getElementById('continuousQuote').textContent = '连续测试已停止';
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initStats();
            console.log('星星话语测试页面加载完成');
            console.log('总诗词数量:', romanticQuotes.length);
            console.log('分类诗词:', categorizedQuotes);
        });
    </script>
</body>
</html>
