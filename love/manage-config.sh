#!/bin/bash

# Love Website 配置管理脚本
# 用于管理网站配置和路径设置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置文件路径
CONFIG_FILE="config.js"
BACKUP_DIR="config-backups"

# 创建备份目录
mkdir -p "$BACKUP_DIR"

# 显示标题
show_title() {
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    Love Website 配置管理器                    ║"
    echo "║                     Configuration Manager                    ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# 显示菜单
show_menu() {
    echo -e "${CYAN}📋 配置管理选项：${NC}"
    echo "1. 📖 查看当前配置"
    echo "2. 🔧 修改API配置"
    echo "3. 🎨 修改字体配置"
    echo "4. 🎬 修改视频配置"
    echo "5. 🌐 修改网站信息"
    echo "6. 💾 备份配置文件"
    echo "7. 🔄 恢复配置文件"
    echo "8. ✅ 验证配置文件"
    echo "9. 🚀 重启服务"
    echo "0. 🚪 退出"
    echo
}

# 备份配置文件
backup_config() {
    if [ -f "$CONFIG_FILE" ]; then
        local timestamp=$(date +"%Y%m%d_%H%M%S")
        local backup_file="$BACKUP_DIR/config_${timestamp}.js"
        cp "$CONFIG_FILE" "$backup_file"
        echo -e "${GREEN}✅ 配置文件已备份到: $backup_file${NC}"
    else
        echo -e "${RED}❌ 配置文件不存在${NC}"
    fi
}

# 查看当前配置
view_config() {
    echo -e "${BLUE}📖 当前配置内容：${NC}"
    if [ -f "$CONFIG_FILE" ]; then
        echo "----------------------------------------"
        cat "$CONFIG_FILE" | head -50
        echo "----------------------------------------"
        echo -e "${YELLOW}💡 显示前50行，完整内容请直接查看 $CONFIG_FILE${NC}"
    else
        echo -e "${RED}❌ 配置文件不存在${NC}"
    fi
}

# 修改API配置
modify_api_config() {
    echo -e "${BLUE}🔧 修改API配置${NC}"
    echo "当前API基础URL: /api"
    echo
    echo "请选择操作："
    echo "1. 修改API基础URL"
    echo "2. 添加新的API端点"
    echo "3. 返回主菜单"
    
    read -p "请输入选择 (1-3): " api_choice
    
    case $api_choice in
        1)
            read -p "请输入新的API基础URL (例如: /api/v2): " new_api_url
            if [ ! -z "$new_api_url" ]; then
                sed -i "s|BASE_URL: '/api'|BASE_URL: '$new_api_url'|g" "$CONFIG_FILE"
                echo -e "${GREEN}✅ API基础URL已更新为: $new_api_url${NC}"
            fi
            ;;
        2)
            read -p "请输入端点名称 (例如: USERS): " endpoint_name
            read -p "请输入端点路径 (例如: /users): " endpoint_path
            if [ ! -z "$endpoint_name" ] && [ ! -z "$endpoint_path" ]; then
                # 这里需要更复杂的文本处理，暂时提示手动编辑
                echo -e "${YELLOW}💡 请手动编辑 $CONFIG_FILE 文件，在 ENDPOINTS 对象中添加：${NC}"
                echo "    $endpoint_name: '$endpoint_path',"
            fi
            ;;
        3)
            return
            ;;
    esac
}

# 修改网站信息
modify_site_info() {
    echo -e "${BLUE}🌐 修改网站信息${NC}"
    echo
    read -p "请输入网站名称 (当前: Love Website): " site_name
    read -p "请输入网站标题 (当前: 我们的爱情故事): " site_title
    read -p "请输入网站描述: " site_desc
    read -p "请输入版本号 (当前: 2.0.0): " version
    
    if [ ! -z "$site_name" ]; then
        sed -i "s|NAME: 'Love Website'|NAME: '$site_name'|g" "$CONFIG_FILE"
    fi
    
    if [ ! -z "$site_title" ]; then
        sed -i "s|TITLE: '我们的爱情故事'|TITLE: '$site_title'|g" "$CONFIG_FILE"
    fi
    
    if [ ! -z "$site_desc" ]; then
        sed -i "s|DESCRIPTION: '记录我们美好的爱情时光'|DESCRIPTION: '$site_desc'|g" "$CONFIG_FILE"
    fi
    
    if [ ! -z "$version" ]; then
        sed -i "s|VERSION: '2.0.0'|VERSION: '$version'|g" "$CONFIG_FILE"
    fi
    
    echo -e "${GREEN}✅ 网站信息已更新${NC}"
}

# 验证配置文件
validate_config() {
    echo -e "${BLUE}✅ 验证配置文件${NC}"
    
    if [ ! -f "$CONFIG_FILE" ]; then
        echo -e "${RED}❌ 配置文件不存在${NC}"
        return 1
    fi
    
    # 使用Node.js验证JavaScript语法
    if command -v node >/dev/null 2>&1; then
        if node -c "$CONFIG_FILE" 2>/dev/null; then
            echo -e "${GREEN}✅ 配置文件语法正确${NC}"
        else
            echo -e "${RED}❌ 配置文件语法错误${NC}"
            node -c "$CONFIG_FILE"
            return 1
        fi
    else
        echo -e "${YELLOW}⚠️  Node.js未安装，跳过语法检查${NC}"
    fi
    
    # 检查必要的配置项
    local required_keys=("API" "PATHS" "FONTS" "SITE")
    for key in "${required_keys[@]}"; do
        if grep -q "$key:" "$CONFIG_FILE"; then
            echo -e "${GREEN}✅ 找到必要配置: $key${NC}"
        else
            echo -e "${RED}❌ 缺少必要配置: $key${NC}"
        fi
    done
}

# 恢复配置文件
restore_config() {
    echo -e "${BLUE}🔄 恢复配置文件${NC}"
    
    if [ ! -d "$BACKUP_DIR" ] || [ -z "$(ls -A $BACKUP_DIR)" ]; then
        echo -e "${RED}❌ 没有找到备份文件${NC}"
        return 1
    fi
    
    echo "可用的备份文件："
    ls -la "$BACKUP_DIR"
    echo
    read -p "请输入要恢复的备份文件名: " backup_file
    
    if [ -f "$BACKUP_DIR/$backup_file" ]; then
        cp "$BACKUP_DIR/$backup_file" "$CONFIG_FILE"
        echo -e "${GREEN}✅ 配置文件已从 $backup_file 恢复${NC}"
    else
        echo -e "${RED}❌ 备份文件不存在${NC}"
    fi
}

# 重启服务
restart_service() {
    echo -e "${BLUE}🚀 重启服务${NC}"
    if [ -f "./manage.sh" ]; then
        ./manage.sh restart
    else
        echo -e "${YELLOW}⚠️  manage.sh 不存在，请手动重启服务${NC}"
    fi
}

# 主循环
main() {
    show_title
    
    while true; do
        show_menu
        read -p "请选择操作 (0-9): " choice
        echo
        
        case $choice in
            1)
                view_config
                ;;
            2)
                modify_api_config
                ;;
            3)
                echo -e "${YELLOW}💡 字体配置较复杂，建议直接编辑 $CONFIG_FILE 文件${NC}"
                ;;
            4)
                echo -e "${YELLOW}💡 视频配置较复杂，建议直接编辑 $CONFIG_FILE 文件${NC}"
                ;;
            5)
                modify_site_info
                ;;
            6)
                backup_config
                ;;
            7)
                restore_config
                ;;
            8)
                validate_config
                ;;
            9)
                restart_service
                ;;
            0)
                echo -e "${GREEN}👋 再见！${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}❌ 无效选择，请重新输入${NC}"
                ;;
        esac
        
        echo
        read -p "按回车键继续..."
        echo
    done
}

# 检查是否在正确的目录
if [ ! -f "server.js" ]; then
    echo -e "${RED}❌ 请在Love网站根目录下运行此脚本${NC}"
    exit 1
fi

# 运行主程序
main
