const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const app = express();
const PORT = process.env.PORT || 1314;

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Serve static files
app.use(express.static(path.join(__dirname)));
app.use('/html', express.static(path.join(__dirname, 'html')));
app.use('/test', express.static(path.join(__dirname, 'test')));
app.use('/background', express.static(path.join(__dirname, 'background')));
app.use('/fonts', express.static(path.join(__dirname, 'fonts')));

// 配置文件特殊处理 - 确保配置文件能被正确访问
app.get('/config.js', (req, res) => {
    res.setHeader('Content-Type', 'application/javascript');
    res.setHeader('Cache-Control', 'no-cache'); // 开发时不缓存配置文件
    res.sendFile(path.join(__dirname, 'config.js'));
});

// Root route - serve the main index page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'html', 'index.html'));
});

// Clean URL routes for all pages
app.get('/together-days', (req, res) => {
    res.sendFile(path.join(__dirname, 'html', 'together-days.html'));
});

app.get('/anniversary', (req, res) => {
    res.sendFile(path.join(__dirname, 'html', 'anniversary.html'));
});

app.get('/meetings', (req, res) => {
    res.sendFile(path.join(__dirname, 'html', 'meetings.html'));
});

app.get('/memorial', (req, res) => {
    res.sendFile(path.join(__dirname, 'html', 'memorial.html'));
});

// Database setup
const dataDir = path.join(__dirname, 'data');
const dbPath = path.join(dataDir, 'love_messages.db');

// 确保data目录存在
if (!require('fs').existsSync(dataDir)) {
    require('fs').mkdirSync(dataDir, { recursive: true });
}

const db = new sqlite3.Database(dbPath);

// 北京时间工具函数
function getBeijingTime(timestamp) {
    const date = new Date(timestamp * 1000);
    // 使用正确的时区转换方法
    return new Date(date.toLocaleString("en-US", {timeZone: "Asia/Shanghai"}));
}

function getBeijingDateString(timestamp) {
    const beijingTime = getBeijingTime(timestamp);
    return beijingTime.toISOString().split('T')[0];
}

function getBeijingDateTimeString(timestamp) {
    const beijingTime = getBeijingTime(timestamp);
    return beijingTime.toISOString().replace('T', ' ').split('.')[0];
}

// Initialize database with new structure
db.serialize(() => {
    // 检查是否为新的数据库结构
    db.get("SELECT name FROM sqlite_master WHERE type='table' AND name='love_messages_new'", (err, row) => {
        if (!row) {
            // 如果没有新表，创建旧表结构（兼容性）
            db.run(`CREATE TABLE IF NOT EXISTS love_messages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                author TEXT NOT NULL CHECK(author IN ('Yu', 'Wang', 'Other')),
                content TEXT NOT NULL,
                created_at INTEGER NOT NULL,
                updated_at INTEGER NOT NULL,
                ip TEXT DEFAULT ''
            )`);

            // Create index for better performance
            db.run(`CREATE INDEX IF NOT EXISTS idx_created_at ON love_messages(created_at)`);
        }
    });

    // 创建时光轴表 (love_timeline)
    db.run(`CREATE TABLE IF NOT EXISTS love_timeline (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        date TEXT NOT NULL,
        title TEXT NOT NULL CHECK(length(title) > 0 AND length(title) <= 200),
        description TEXT NOT NULL CHECK(length(description) > 0 AND length(description) <= 2000),
        created_timestamp INTEGER NOT NULL,
        updated_timestamp INTEGER NOT NULL,
        beijing_date TEXT NOT NULL,
        beijing_datetime TEXT NOT NULL,
        status TEXT DEFAULT 'active' CHECK(status IN ('active', 'deleted')),
        sort_order INTEGER DEFAULT 0,
        CHECK(created_timestamp > 0),
        CHECK(updated_timestamp >= created_timestamp)
    )`);

    // 创建美好瞬间表 (love_memories)
    db.run(`CREATE TABLE IF NOT EXISTS love_memories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        icon TEXT NOT NULL CHECK(length(icon) > 0 AND length(icon) <= 100),
        title TEXT NOT NULL CHECK(length(title) > 0 AND length(title) <= 200),
        content TEXT NOT NULL CHECK(length(content) > 0 AND length(content) <= 2000),
        created_timestamp INTEGER NOT NULL,
        updated_timestamp INTEGER NOT NULL,
        beijing_date TEXT NOT NULL,
        beijing_datetime TEXT NOT NULL,
        status TEXT DEFAULT 'active' CHECK(status IN ('active', 'deleted')),
        sort_order INTEGER DEFAULT 0,
        CHECK(created_timestamp > 0),
        CHECK(updated_timestamp >= created_timestamp)
    )`);

    // 创建现代情话表 (modern_love_quotes)
    db.run(`CREATE TABLE IF NOT EXISTS modern_love_quotes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        content TEXT NOT NULL CHECK(length(content) > 0 AND length(content) <= 500),
        source TEXT DEFAULT '' CHECK(length(source) <= 200),
        category TEXT DEFAULT 'general' CHECK(category IN ('romantic', 'sweet', 'deep', 'funny', 'proposal', 'anniversary', 'general')),
        language TEXT DEFAULT 'zh' CHECK(language IN ('zh', 'en')),
        created_timestamp INTEGER NOT NULL,
        updated_timestamp INTEGER NOT NULL,
        beijing_date TEXT NOT NULL,
        beijing_datetime TEXT NOT NULL,
        status TEXT DEFAULT 'active' CHECK(status IN ('active', 'deleted')),
        sort_order INTEGER DEFAULT 0,
        tags TEXT DEFAULT '',
        popularity_score INTEGER DEFAULT 0,
        CHECK(created_timestamp > 0),
        CHECK(updated_timestamp >= created_timestamp)
    )`);

    // 创建索引以提高查询性能
    db.run(`CREATE INDEX IF NOT EXISTS idx_timeline_created_timestamp ON love_timeline(created_timestamp)`);
    db.run(`CREATE INDEX IF NOT EXISTS idx_timeline_status ON love_timeline(status)`);
    db.run(`CREATE INDEX IF NOT EXISTS idx_timeline_sort_order ON love_timeline(sort_order)`);

    db.run(`CREATE INDEX IF NOT EXISTS idx_memories_created_timestamp ON love_memories(created_timestamp)`);
    db.run(`CREATE INDEX IF NOT EXISTS idx_memories_status ON love_memories(status)`);
    db.run(`CREATE INDEX IF NOT EXISTS idx_memories_sort_order ON love_memories(sort_order)`);

    // 现代情话表索引
    db.run(`CREATE INDEX IF NOT EXISTS idx_modern_quotes_created_timestamp ON modern_love_quotes(created_timestamp)`);
    db.run(`CREATE INDEX IF NOT EXISTS idx_modern_quotes_status ON modern_love_quotes(status)`);
    db.run(`CREATE INDEX IF NOT EXISTS idx_modern_quotes_category ON modern_love_quotes(category)`);
    db.run(`CREATE INDEX IF NOT EXISTS idx_modern_quotes_popularity ON modern_love_quotes(popularity_score)`);

    // 初始化时光轴数据
    db.get("SELECT COUNT(*) as count FROM love_timeline WHERE status = 'active'", (err, row) => {
        if (!err && row.count === 0) {
            const now = getCurrentTimestamp();
            const initialTimelineData = [
                {
                    date: '2023年4月23日',
                    title: '💕 确定关系',
                    description: '这一天，我们正式确定了恋爱关系。从朋友到恋人，这是我们爱情故事最重要的开始。那一刻的心跳，至今还记得那么清楚。',
                    sort_order: 1
                },
                {
                    date: '2023年5月1日',
                    title: '🌸 第一次约会',
                    description: '劳动节的那天，我们有了第一次正式的约会。一起看电影，一起吃饭，一起在公园里漫步。那种紧张又甜蜜的感觉，现在想起来还会脸红。',
                    sort_order: 2
                },
                {
                    date: '2023年6月15日',
                    title: '🎁 第一份礼物',
                    description: '你送给我的第一份礼物，一条精美的项链。你说希望它能时刻陪伴在我身边，就像你的爱一样。那份心意比礼物本身更珍贵。',
                    sort_order: 3
                },
                {
                    date: '2023年8月20日',
                    title: '🌅 第一次旅行',
                    description: '我们的第一次旅行，去了海边看日出。清晨的海风，金色的阳光，还有你温暖的手。那是我见过最美的日出，因为有你在身边。',
                    sort_order: 4
                },
                {
                    date: '2023年10月31日',
                    title: '🎃 万圣节惊喜',
                    description: '万圣节那天，你精心准备了一个小惊喜。虽然不是什么大事，但那份用心让我感动不已。原来爱情就是这些小小的惊喜组成的。',
                    sort_order: 5
                },
                {
                    date: '2024年1月1日',
                    title: '🎊 新年愿望',
                    description: '新年的第一天，我们一起许下了愿望。希望我们的爱情能够长长久久，希望每一年都能和你一起度过。这个愿望，我想要实现一辈子。',
                    sort_order: 6
                }
            ];

            initialTimelineData.forEach((item, index) => {
                const timestamp = now + index;
                const beijingDate = getBeijingDateString(timestamp);
                const beijingDateTime = getBeijingDateTimeString(timestamp);

                db.run(
                    `INSERT INTO love_timeline (date, title, description, created_timestamp, updated_timestamp, beijing_date, beijing_datetime, status, sort_order)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                    [item.date, item.title, item.description, timestamp, timestamp, beijingDate, beijingDateTime, 'active', item.sort_order]
                );
            });
        }
    });

    // 初始化美好瞬间数据
    db.get("SELECT COUNT(*) as count FROM love_memories WHERE status = 'active'", (err, row) => {
        if (!err && row.count === 0) {
            const now = getCurrentTimestamp();
            const initialMemoriesData = [
                {
                    icon: 'fas fa-coffee',
                    title: '晨间咖啡',
                    content: '每个周末的早晨，我们都会一起去那家小咖啡店。你总是点拿铁，我总是点美式。简单的幸福，就是这样。',
                    sort_order: 1
                },
                {
                    icon: 'fas fa-moon',
                    title: '深夜电话',
                    content: '那些聊到深夜的电话，从生活琐事到人生理想。你的声音是我最好的安眠曲，也是我最温暖的陪伴。',
                    sort_order: 2
                },
                {
                    icon: 'fas fa-umbrella',
                    title: '雨中漫步',
                    content: '那次突然下雨，我们没有带伞，却在雨中慢慢走着。你说这样的雨天很浪漫，我觉得有你在身边就是浪漫。',
                    sort_order: 3
                },
                {
                    icon: 'fas fa-star',
                    title: '看星星',
                    content: '在那个没有光污染的小镇，我们一起看满天繁星。你指着最亮的那颗星说，那是我们的爱情之星。',
                    sort_order: 4
                },
                {
                    icon: 'fas fa-music',
                    title: '音乐分享',
                    content: '每当发现好听的歌，我们总是第一时间分享给对方。那些旋律记录着我们的心情，成为了专属回忆。',
                    sort_order: 5
                },
                {
                    icon: 'fas fa-book',
                    title: '读书时光',
                    content: '安静的午后，我们各自捧着喜欢的书，偶尔分享有趣的段落。那种默契的陪伴，胜过千言万语。',
                    sort_order: 6
                }
            ];

            initialMemoriesData.forEach((item, index) => {
                const timestamp = now + index + 100;
                const beijingDate = getBeijingDateString(timestamp);
                const beijingDateTime = getBeijingDateTimeString(timestamp);

                db.run(
                    `INSERT INTO love_memories (icon, title, content, created_timestamp, updated_timestamp, beijing_date, beijing_datetime, status, sort_order)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                    [item.icon, item.title, item.content, timestamp, timestamp, beijingDate, beijingDateTime, 'active', item.sort_order]
                );
            });
        }
    });

    // 初始化现代情话数据
    db.get("SELECT COUNT(*) as count FROM modern_love_quotes WHERE status = 'active'", (err, row) => {
        if (!err && row.count === 0) {
            const now = getCurrentTimestamp();
            // 导入现代情话数据
            const { modernLoveQuotes } = require('./modern-quotes-data.js');

            modernLoveQuotes.forEach((item, index) => {
                const timestamp = now + index + 300;
                const beijingDate = getBeijingDateString(timestamp);
                const beijingDateTime = getBeijingDateTimeString(timestamp);

                db.run(
                    `INSERT INTO modern_love_quotes (content, source, category, language, created_timestamp, updated_timestamp, beijing_date, beijing_datetime, status, sort_order, popularity_score)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                    [item.content, item.source, item.category, 'zh', timestamp, timestamp, beijingDate, beijingDateTime, 'active', index + 1, Math.floor(Math.random() * 100)]
                );
            });

            console.log(`Initialized ${modernLoveQuotes.length} modern love quotes`);
        }
    });
});

// Helper function to get current timestamp
const getCurrentTimestamp = () => Math.floor(Date.now() / 1000);

// Helper function to format timestamp for frontend
const formatTimestamp = (timestamp) => {
    return new Date(timestamp * 1000).toISOString();
};

// API Routes

// 检查使用哪个表
function getTableName(callback) {
    db.get("SELECT name FROM sqlite_master WHERE type='table' AND name='love_messages_new'", (err, row) => {
        if (err) {
            callback('love_messages'); // 默认使用旧表
        } else {
            callback(row ? 'love_messages_new' : 'love_messages');
        }
    });
}

// Get all messages
app.get('/api/messages', (req, res) => {
    getTableName((tableName) => {
        const query = tableName === 'love_messages_new'
            ? 'SELECT * FROM love_messages_new WHERE status = "active" ORDER BY created_timestamp DESC'
            : 'SELECT * FROM love_messages ORDER BY created_at DESC';

        db.all(query, [], (err, rows) => {
            if (err) {
                console.error('Error fetching messages:', err);
                return res.json({
                    success: false,
                    message: '获取留言失败: ' + err.message
                });
            }

            // Format messages for frontend
            const messages = rows.map(row => {
                if (tableName === 'love_messages_new') {
                    return {
                        id: row.id,
                        author: row.author,
                        content: row.content,
                        created_at: row.created_timestamp,
                        timestamp: formatTimestamp(row.created_timestamp),
                        beijing_date: row.beijing_date,
                        beijing_datetime: row.beijing_datetime
                    };
                } else {
                    return {
                        id: row.id,
                        author: row.author,
                        content: row.content,
                        created_at: row.created_at,
                        timestamp: formatTimestamp(row.created_at)
                    };
                }
            });

            res.json({
                success: true,
                message: '',
                data: messages
            });
        });
    });
});

// Get messages with pagination
app.get('/api/messages/paginated', (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const pageSize = Math.min(parseInt(req.query.page_size) || 20, 100);
    const offset = (page - 1) * pageSize;
    
    getTableName((tableName) => {
        // Get total count
        const countQuery = tableName === 'love_messages_new'
            ? 'SELECT COUNT(*) as total FROM love_messages_new WHERE status = "active"'
            : 'SELECT COUNT(*) as total FROM love_messages';

        db.get(countQuery, [], (err, countRow) => {
        if (err) {
            console.error('Error counting messages:', err);
            return res.json({
                success: false,
                message: '获取留言失败: ' + err.message
            });
        }
        
        const total = countRow.total;
        
        // Get paginated messages
        const query = tableName === 'love_messages_new'
            ? 'SELECT * FROM love_messages_new WHERE status = "active" ORDER BY created_timestamp DESC LIMIT ? OFFSET ?'
            : 'SELECT * FROM love_messages ORDER BY created_at DESC LIMIT ? OFFSET ?';

        db.all(query, [pageSize, offset],
            (err, rows) => {
                if (err) {
                    console.error('Error fetching paginated messages:', err);
                    return res.json({
                        success: false,
                        message: '获取留言失败: ' + err.message
                    });
                }
                
                // Format messages for frontend
                const messages = rows.map(row => ({
                    id: row.id,
                    author: row.author,
                    content: row.content,
                    created_at: row.created_at,
                    timestamp: formatTimestamp(row.created_at)
                }));
                
                res.json({
                    success: true,
                    message: '',
                    data: {
                        items: messages,
                        total: total,
                        page: page,
                        page_size: pageSize
                    }
                });
            }
        );
        });
    });
});

// Create new message
app.post('/api/messages', (req, res) => {
    const { author, content } = req.body;
    
    // Validation
    if (!author || !content) {
        return res.status(400).json({
            success: false,
            message: '作者和内容不能为空'
        });
    }
    
    if (!['Yu', 'Wang', 'Other'].includes(author)) {
        return res.status(400).json({
            success: false,
            message: '作者字段只能是"Yu"、"Wang"或"Other"'
        });
    }
    
    const trimmedContent = content.trim();
    if (trimmedContent.length === 0) {
        return res.status(400).json({
            success: false,
            message: '留言内容不能为空'
        });
    }
    
    if (trimmedContent.length > 1000) {
        return res.status(400).json({
            success: false,
            message: '留言内容不能超过1000个字符'
        });
    }
    
    const now = getCurrentTimestamp();
    const clientIP = req.ip || req.connection.remoteAddress || '';

    getTableName((tableName) => {
        if (tableName === 'love_messages_new') {
            // 使用新表结构
            const beijingDate = getBeijingDateString(now);
            const beijingDateTime = getBeijingDateTimeString(now);

            db.run(
                `INSERT INTO love_messages_new
                (author, content, created_timestamp, updated_timestamp, beijing_date, beijing_datetime, client_ip, status, version)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [author, trimmedContent, now, now, beijingDate, beijingDateTime, clientIP, 'active', 1],
                function(err) {
                    if (err) {
                        console.error('Error creating message:', err);
                        return res.status(500).json({
                            success: false,
                            message: '创建留言失败: ' + err.message
                        });
                    }

                    // Return the created message
                    const newMessage = {
                        id: this.lastID,
                        author: author,
                        content: trimmedContent,
                        created_at: now,
                        timestamp: formatTimestamp(now),
                        beijing_date: beijingDate,
                        beijing_datetime: beijingDateTime
                    };

                    res.json({
                        success: true,
                        message: '留言创建成功',
                        data: newMessage
                    });
                }
            );
        } else {
            // 使用旧表结构
            db.run(
                'INSERT INTO love_messages (author, content, created_at, updated_at, ip) VALUES (?, ?, ?, ?, ?)',
                [author, trimmedContent, now, now, clientIP],
                function(err) {
                    if (err) {
                        console.error('Error creating message:', err);
                        return res.status(500).json({
                            success: false,
                            message: '创建留言失败: ' + err.message
                        });
                    }

                    // Return the created message
                    const newMessage = {
                        id: this.lastID,
                        author: author,
                        content: trimmedContent,
                        created_at: now,
                        timestamp: formatTimestamp(now)
                    };

                    res.json({
                        success: true,
                        message: '留言创建成功',
                        data: newMessage
                    });
                }
            );
        }
    });
});

// Update message
app.put('/api/messages/:id', (req, res) => {
    const messageId = parseInt(req.params.id);
    const { content } = req.body;
    
    if (!content) {
        return res.status(400).json({
            success: false,
            message: '内容不能为空'
        });
    }
    
    const trimmedContent = content.trim();
    if (trimmedContent.length === 0) {
        return res.status(400).json({
            success: false,
            message: '留言内容不能为空'
        });
    }
    
    if (trimmedContent.length > 1000) {
        return res.status(400).json({
            success: false,
            message: '留言内容不能超过1000个字符'
        });
    }
    
    const now = getCurrentTimestamp();

    getTableName((tableName) => {
        const query = tableName === 'love_messages_new'
            ? 'UPDATE love_messages_new SET content = ?, updated_timestamp = ? WHERE id = ?'
            : 'UPDATE love_messages SET content = ?, updated_at = ? WHERE id = ?';

        db.run(query, [trimmedContent, now, messageId], function(err) {
            if (err) {
                console.error('Error updating message:', err);
                return res.status(500).json({
                    success: false,
                    message: '更新留言失败: ' + err.message
                });
            }

            if (this.changes === 0) {
                return res.status(404).json({
                    success: false,
                    message: '留言不存在'
                });
            }

            res.json({
                success: true,
                message: '留言更新成功'
            });
        });
    });
});

// Delete message
app.delete('/api/messages/:id', (req, res) => {
    const messageId = parseInt(req.params.id);

    getTableName((tableName) => {
        const query = tableName === 'love_messages_new'
            ? 'DELETE FROM love_messages_new WHERE id = ?'
            : 'DELETE FROM love_messages WHERE id = ?';

        db.run(query, [messageId], function(err) {
            if (err) {
                console.error('Error deleting message:', err);
                return res.status(500).json({
                    success: false,
                    message: '删除留言失败: ' + err.message
                });
            }

            if (this.changes === 0) {
                return res.status(404).json({
                    success: false,
                    message: '留言不存在'
                });
            }

            res.json({
                success: true,
                message: '留言删除成功'
            });
        });
    });
});

// Get message statistics
app.get('/api/messages/stats', (req, res) => {
    getTableName((tableName) => {
        const query = tableName === 'love_messages_new'
            ? 'SELECT COUNT(*) as total FROM love_messages_new WHERE status = "active"'
            : 'SELECT COUNT(*) as total FROM love_messages';

        db.get(query, [], (err, row) => {
        if (err) {
            console.error('Error getting stats:', err);
            return res.json({
                success: false,
                message: '获取统计信息失败: ' + err.message
            });
        }
        
        res.json({
            success: true,
            message: '',
            data: {
                total_messages: row.total
            }
        });
        });
    });
});

// ===== 时光轴 API 接口 =====

// 获取所有时光轴数据
app.get('/api/timeline', (req, res) => {
    db.all(
        'SELECT * FROM love_timeline WHERE status = "active" ORDER BY sort_order ASC, created_timestamp ASC',
        [],
        (err, rows) => {
            if (err) {
                console.error('Error fetching timeline:', err);
                return res.json({
                    success: false,
                    message: '获取时光轴数据失败: ' + err.message
                });
            }

            const timeline = rows.map(row => ({
                id: row.id,
                date: row.date,
                title: row.title,
                description: row.description,
                created_at: row.created_timestamp,
                timestamp: formatTimestamp(row.created_timestamp),
                beijing_date: row.beijing_date,
                beijing_datetime: row.beijing_datetime,
                sort_order: row.sort_order
            }));

            res.json({
                success: true,
                message: '',
                data: timeline
            });
        }
    );
});

// 创建新的时光轴记录
app.post('/api/timeline', (req, res) => {
    const { date, title, description, sort_order } = req.body;

    // 数据验证
    if (!date || !title || !description) {
        return res.status(400).json({
            success: false,
            message: '日期、标题和描述不能为空'
        });
    }

    const trimmedDate = date.trim();
    const trimmedTitle = title.trim();
    const trimmedDescription = description.trim();

    if (trimmedDate.length === 0 || trimmedTitle.length === 0 || trimmedDescription.length === 0) {
        return res.status(400).json({
            success: false,
            message: '日期、标题和描述不能为空'
        });
    }

    if (trimmedTitle.length > 200) {
        return res.status(400).json({
            success: false,
            message: '标题不能超过200个字符'
        });
    }

    if (trimmedDescription.length > 2000) {
        return res.status(400).json({
            success: false,
            message: '描述不能超过2000个字符'
        });
    }

    const now = getCurrentTimestamp();
    const beijingDate = getBeijingDateString(now);
    const beijingDateTime = getBeijingDateTimeString(now);
    const finalSortOrder = sort_order || 0;

    db.run(
        `INSERT INTO love_timeline (date, title, description, created_timestamp, updated_timestamp, beijing_date, beijing_datetime, status, sort_order)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [trimmedDate, trimmedTitle, trimmedDescription, now, now, beijingDate, beijingDateTime, 'active', finalSortOrder],
        function(err) {
            if (err) {
                console.error('Error creating timeline:', err);
                return res.status(500).json({
                    success: false,
                    message: '创建时光轴记录失败: ' + err.message
                });
            }

            const newTimeline = {
                id: this.lastID,
                date: trimmedDate,
                title: trimmedTitle,
                description: trimmedDescription,
                created_at: now,
                timestamp: formatTimestamp(now),
                beijing_date: beijingDate,
                beijing_datetime: beijingDateTime,
                sort_order: finalSortOrder
            };

            res.json({
                success: true,
                message: '时光轴记录创建成功',
                data: newTimeline
            });
        }
    );
});

// 更新时光轴记录
app.put('/api/timeline/:id', (req, res) => {
    const timelineId = parseInt(req.params.id);
    const { date, title, description, sort_order } = req.body;

    // 数据验证
    if (!date || !title || !description) {
        return res.status(400).json({
            success: false,
            message: '日期、标题和描述不能为空'
        });
    }

    const trimmedDate = date.trim();
    const trimmedTitle = title.trim();
    const trimmedDescription = description.trim();

    if (trimmedDate.length === 0 || trimmedTitle.length === 0 || trimmedDescription.length === 0) {
        return res.status(400).json({
            success: false,
            message: '日期、标题和描述不能为空'
        });
    }

    if (trimmedTitle.length > 200) {
        return res.status(400).json({
            success: false,
            message: '标题不能超过200个字符'
        });
    }

    if (trimmedDescription.length > 2000) {
        return res.status(400).json({
            success: false,
            message: '描述不能超过2000个字符'
        });
    }

    const now = getCurrentTimestamp();
    const finalSortOrder = sort_order || 0;

    db.run(
        'UPDATE love_timeline SET date = ?, title = ?, description = ?, updated_timestamp = ?, sort_order = ? WHERE id = ? AND status = "active"',
        [trimmedDate, trimmedTitle, trimmedDescription, now, finalSortOrder, timelineId],
        function(err) {
            if (err) {
                console.error('Error updating timeline:', err);
                return res.status(500).json({
                    success: false,
                    message: '更新时光轴记录失败: ' + err.message
                });
            }

            if (this.changes === 0) {
                return res.status(404).json({
                    success: false,
                    message: '时光轴记录不存在'
                });
            }

            res.json({
                success: true,
                message: '时光轴记录更新成功'
            });
        }
    );
});

// 删除时光轴记录
app.delete('/api/timeline/:id', (req, res) => {
    const timelineId = parseInt(req.params.id);

    db.run(
        'DELETE FROM love_timeline WHERE id = ?',
        [timelineId],
        function(err) {
            if (err) {
                console.error('Error deleting timeline:', err);
                return res.status(500).json({
                    success: false,
                    message: '删除时光轴记录失败: ' + err.message
                });
            }

            if (this.changes === 0) {
                return res.status(404).json({
                    success: false,
                    message: '时光轴记录不存在'
                });
            }

            res.json({
                success: true,
                message: '时光轴记录删除成功'
            });
        }
    );
});

// ===== 美好瞬间 API 接口 =====

// 获取所有美好瞬间数据
app.get('/api/memories', (req, res) => {
    db.all(
        'SELECT * FROM love_memories WHERE status = "active" ORDER BY sort_order ASC, created_timestamp ASC',
        [],
        (err, rows) => {
            if (err) {
                console.error('Error fetching memories:', err);
                return res.json({
                    success: false,
                    message: '获取美好瞬间数据失败: ' + err.message
                });
            }

            const memories = rows.map(row => ({
                id: row.id,
                icon: row.icon,
                title: row.title,
                content: row.content,
                created_at: row.created_timestamp,
                timestamp: formatTimestamp(row.created_timestamp),
                beijing_date: row.beijing_date,
                beijing_datetime: row.beijing_datetime,
                sort_order: row.sort_order
            }));

            res.json({
                success: true,
                message: '',
                data: memories
            });
        }
    );
});

// 创建新的美好瞬间记录
app.post('/api/memories', (req, res) => {
    const { icon, title, content, sort_order } = req.body;

    // 数据验证
    if (!icon || !title || !content) {
        return res.status(400).json({
            success: false,
            message: '图标、标题和内容不能为空'
        });
    }

    const trimmedIcon = icon.trim();
    const trimmedTitle = title.trim();
    const trimmedContent = content.trim();

    if (trimmedIcon.length === 0 || trimmedTitle.length === 0 || trimmedContent.length === 0) {
        return res.status(400).json({
            success: false,
            message: '图标、标题和内容不能为空'
        });
    }

    if (trimmedIcon.length > 100) {
        return res.status(400).json({
            success: false,
            message: '图标类名不能超过100个字符'
        });
    }

    if (trimmedTitle.length > 200) {
        return res.status(400).json({
            success: false,
            message: '标题不能超过200个字符'
        });
    }

    if (trimmedContent.length > 2000) {
        return res.status(400).json({
            success: false,
            message: '内容不能超过2000个字符'
        });
    }

    const now = getCurrentTimestamp();
    const beijingDate = getBeijingDateString(now);
    const beijingDateTime = getBeijingDateTimeString(now);
    const finalSortOrder = sort_order || 0;

    db.run(
        `INSERT INTO love_memories (icon, title, content, created_timestamp, updated_timestamp, beijing_date, beijing_datetime, status, sort_order)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [trimmedIcon, trimmedTitle, trimmedContent, now, now, beijingDate, beijingDateTime, 'active', finalSortOrder],
        function(err) {
            if (err) {
                console.error('Error creating memory:', err);
                return res.status(500).json({
                    success: false,
                    message: '创建美好瞬间记录失败: ' + err.message
                });
            }

            const newMemory = {
                id: this.lastID,
                icon: trimmedIcon,
                title: trimmedTitle,
                content: trimmedContent,
                created_at: now,
                timestamp: formatTimestamp(now),
                beijing_date: beijingDate,
                beijing_datetime: beijingDateTime,
                sort_order: finalSortOrder
            };

            res.json({
                success: true,
                message: '美好瞬间记录创建成功',
                data: newMemory
            });
        }
    );
});

// 更新美好瞬间记录
app.put('/api/memories/:id', (req, res) => {
    const memoryId = parseInt(req.params.id);
    const { icon, title, content, sort_order } = req.body;

    // 数据验证
    if (!icon || !title || !content) {
        return res.status(400).json({
            success: false,
            message: '图标、标题和内容不能为空'
        });
    }

    const trimmedIcon = icon.trim();
    const trimmedTitle = title.trim();
    const trimmedContent = content.trim();

    if (trimmedIcon.length === 0 || trimmedTitle.length === 0 || trimmedContent.length === 0) {
        return res.status(400).json({
            success: false,
            message: '图标、标题和内容不能为空'
        });
    }

    if (trimmedIcon.length > 100) {
        return res.status(400).json({
            success: false,
            message: '图标类名不能超过100个字符'
        });
    }

    if (trimmedTitle.length > 200) {
        return res.status(400).json({
            success: false,
            message: '标题不能超过200个字符'
        });
    }

    if (trimmedContent.length > 2000) {
        return res.status(400).json({
            success: false,
            message: '内容不能超过2000个字符'
        });
    }

    const now = getCurrentTimestamp();
    const finalSortOrder = sort_order || 0;

    db.run(
        'UPDATE love_memories SET icon = ?, title = ?, content = ?, updated_timestamp = ?, sort_order = ? WHERE id = ? AND status = "active"',
        [trimmedIcon, trimmedTitle, trimmedContent, now, finalSortOrder, memoryId],
        function(err) {
            if (err) {
                console.error('Error updating memory:', err);
                return res.status(500).json({
                    success: false,
                    message: '更新美好瞬间记录失败: ' + err.message
                });
            }

            if (this.changes === 0) {
                return res.status(404).json({
                    success: false,
                    message: '美好瞬间记录不存在'
                });
            }

            res.json({
                success: true,
                message: '美好瞬间记录更新成功'
            });
        }
    );
});

// 删除美好瞬间记录
app.delete('/api/memories/:id', (req, res) => {
    const memoryId = parseInt(req.params.id);

    db.run(
        'DELETE FROM love_memories WHERE id = ?',
        [memoryId],
        function(err) {
            if (err) {
                console.error('Error deleting memory:', err);
                return res.status(500).json({
                    success: false,
                    message: '删除美好瞬间记录失败: ' + err.message
                });
            }

            if (this.changes === 0) {
                return res.status(404).json({
                    success: false,
                    message: '美好瞬间记录不存在'
                });
            }

            res.json({
                success: true,
                message: '美好瞬间记录删除成功'
            });
        }
    );
});

// ===== 现代情话 API 接口 =====

// 获取所有现代情话
app.get('/api/modern-quotes', (req, res) => {
    const category = req.query.category;
    const limit = parseInt(req.query.limit) || 0;

    let query = 'SELECT * FROM modern_love_quotes WHERE status = "active"';
    let params = [];

    if (category && category !== 'all') {
        query += ' AND category = ?';
        params.push(category);
    }

    query += ' ORDER BY popularity_score DESC, created_timestamp DESC';

    if (limit > 0) {
        query += ' LIMIT ?';
        params.push(limit);
    }

    db.all(query, params, (err, rows) => {
        if (err) {
            console.error('Error fetching modern quotes:', err);
            return res.json({
                success: false,
                message: '获取现代情话失败: ' + err.message
            });
        }

        const quotes = rows.map(row => ({
            id: row.id,
            content: row.content,
            source: row.source,
            category: row.category,
            language: row.language,
            created_at: row.created_timestamp,
            timestamp: formatTimestamp(row.created_timestamp),
            beijing_date: row.beijing_date,
            beijing_datetime: row.beijing_datetime,
            tags: row.tags ? JSON.parse(row.tags) : [],
            popularity_score: row.popularity_score
        }));

        res.json({
            success: true,
            data: quotes,
            total: quotes.length
        });
    });
});

// 获取随机现代情话
app.get('/api/modern-quotes/random', (req, res) => {
    const category = req.query.category;
    const count = Math.min(parseInt(req.query.count) || 1, 10);

    let query = 'SELECT * FROM modern_love_quotes WHERE status = "active"';
    let params = [];

    if (category && category !== 'all') {
        query += ' AND category = ?';
        params.push(category);
    }

    query += ' ORDER BY RANDOM() LIMIT ?';
    params.push(count);

    db.all(query, params, (err, rows) => {
        if (err) {
            console.error('Error fetching random modern quotes:', err);
            return res.json({
                success: false,
                message: '获取随机现代情话失败: ' + err.message
            });
        }

        const quotes = rows.map(row => ({
            id: row.id,
            content: row.content,
            source: row.source,
            category: row.category,
            language: row.language,
            created_at: row.created_timestamp,
            timestamp: formatTimestamp(row.created_timestamp),
            beijing_date: row.beijing_date,
            beijing_datetime: row.beijing_datetime,
            tags: row.tags ? JSON.parse(row.tags) : [],
            popularity_score: row.popularity_score
        }));

        res.json({
            success: true,
            data: count === 1 ? quotes[0] : quotes,
            total: quotes.length
        });
    });
});

// 按分类获取现代情话
app.get('/api/modern-quotes/category/:category', (req, res) => {
    const category = req.params.category;
    const limit = parseInt(req.query.limit) || 0;

    let query = 'SELECT * FROM modern_love_quotes WHERE status = "active" AND category = ? ORDER BY popularity_score DESC, created_timestamp DESC';
    let params = [category];

    if (limit > 0) {
        query += ' LIMIT ?';
        params.push(limit);
    }

    db.all(query, params, (err, rows) => {
        if (err) {
            console.error('Error fetching modern quotes by category:', err);
            return res.json({
                success: false,
                message: '获取分类现代情话失败: ' + err.message
            });
        }

        const quotes = rows.map(row => ({
            id: row.id,
            content: row.content,
            source: row.source,
            category: row.category,
            language: row.language,
            created_at: row.created_timestamp,
            timestamp: formatTimestamp(row.created_timestamp),
            beijing_date: row.beijing_date,
            beijing_datetime: row.beijing_datetime,
            tags: row.tags ? JSON.parse(row.tags) : [],
            popularity_score: row.popularity_score
        }));

        res.json({
            success: true,
            data: quotes,
            category: category,
            total: quotes.length
        });
    });
});

// 获取现代情话统计信息
app.get('/api/modern-quotes/stats', (req, res) => {
    const queries = [
        'SELECT COUNT(*) as total FROM modern_love_quotes WHERE status = "active"',
        'SELECT category, COUNT(*) as count FROM modern_love_quotes WHERE status = "active" GROUP BY category',
        'SELECT AVG(popularity_score) as avg_popularity FROM modern_love_quotes WHERE status = "active"'
    ];

    Promise.all(queries.map(query => new Promise((resolve, reject) => {
        db.all(query, [], (err, rows) => {
            if (err) reject(err);
            else resolve(rows);
        });
    }))).then(results => {
        const [totalResult, categoryResult, popularityResult] = results;

        res.json({
            success: true,
            data: {
                total: totalResult[0].total,
                categories: categoryResult,
                average_popularity: Math.round(popularityResult[0].avg_popularity || 0)
            }
        });
    }).catch(err => {
        console.error('Error getting modern quotes stats:', err);
        res.json({
            success: false,
            message: '获取现代情话统计失败: ' + err.message
        });
    });
});

// Health check
app.get('/api/health', (req, res) => {
    res.json({
        success: true,
        message: 'Love site backend is running',
        timestamp: new Date().toISOString()
    });
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Unhandled error:', err);
    res.status(500).json({
        success: false,
        message: '服务器内部错误'
    });
});

// Start server
app.listen(PORT, () => {
    console.log(`Love site backend server is running on port ${PORT}`);
    console.log(`Database path: ${dbPath}`);
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\nShutting down gracefully...');
    db.close((err) => {
        if (err) {
            console.error('Error closing database:', err);
        } else {
            console.log('Database connection closed.');
        }
        process.exit(0);
    });
});
